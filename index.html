<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير المشاريع</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-gradient: linear-gradient(145deg, #ffffff 0%, #f8faff 100%);
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-light: #718096;
            --border-color: #e2e8f0;
            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --success-color: #48bb78;
            --danger-color: #f56565;
            --warning-color: #ed8936;
            --info-color: #4299e1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: var(--background-gradient);
            color: var(--text-primary);
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            min-height: 100vh;
        }

        header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: transform 0.4s ease-in-out;
        }

        header h1 {
            color: white;
            margin-bottom: 1rem;
            font-size: 3rem;
            font-weight: 900;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #ffffff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 12px 24px;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        nav ul li a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        nav ul li a:hover::before {
            left: 100%;
        }

        nav ul li a:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        main {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page {
            display: none;
            background: var(--card-gradient);
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-large);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
        }

        .page.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h2 {
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 3rem;
            font-size: 2.5rem;
            font-weight: 700;
            position: relative;
        }

        h2::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -10px;
            width: 100px;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .login-container {
            display: flex;
            flex-direction: column;
            width: 420px;
            margin: 3rem auto;
            padding: 3rem;
            background: var(--card-gradient);
            border-radius: 20px;
            box-shadow: var(--shadow-large);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: right;
        }

        .login-container label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1.1rem;
        }

        .login-container input {
            margin-bottom: 1.5rem;
            padding: 15px 20px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            text-align: right;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .login-container input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
            transform: translateY(-1px);
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: inherit;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #38a169);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #e53e3e);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #dd6b20);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-color), #3182ce);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #718096, #4a5568);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .login-container button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-medium);
            padding: 15px 30px;
            font-family: inherit;
        }

        .login-container button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        #login-message {
            color: var(--danger-color);
            margin-top: 1rem;
            text-align: center;
            font-weight: 600;
            padding: 10px;
            border-radius: 8px;
            background: rgba(245, 101, 101, 0.1);
        }

        .form-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 2.5rem;
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--border-color);
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 15px 18px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            color: var(--text-primary);
            background: white;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        /* تصميم خاص لحقل اسم المستفيد */
        #beneficiary-name {
            padding: 20px 25px !important;
            font-size: 1.2rem !important;
            font-weight: 600 !important;
            border: 3px solid var(--border-color) !important;
            border-radius: 15px !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
            min-height: 60px !important;
        }

        #beneficiary-name:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15) !important;
            background: white !important;
            transform: translateY(-2px) !important;
        }

        /* حاوية الاقتراح التلقائي */
        .autocomplete-container {
            position: relative;
            width: 100%;
        }

        .suggestions-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 12px 12px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .suggestions-list.show {
            display: block;
        }

        .suggestion-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .suggestion-item:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            transform: translateX(5px);
        }

        .suggestion-item:last-child {
            border-bottom: none;
            border-radius: 0 0 10px 10px;
        }

        .suggestion-item.highlighted {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            color: var(--primary-color);
            font-weight: 600;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
            transform: translateY(-1px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .buttons-container {
            grid-column: 1 / -1;
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .dashboard-section {
            background: var(--card-gradient);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: var(--shadow-large);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dashboard-section h3 {
            text-align: center;
            margin-bottom: 2.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.2rem;
            font-weight: 700;
            position: relative;
        }

        .dashboard-section h3::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -10px;
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .dashboard-section h4 {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-primary);
            font-size: 1.8rem;
            font-weight: 600;
        }

        .dashboard-portals {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .portal-card {
            background: linear-gradient(145deg, #ffffff, #f7fafc);
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: var(--shadow-medium);
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .portal-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .portal-card:hover::before {
            opacity: 1;
            animation: shimmer 1.5s ease-in-out;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .portal-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-large);
            border-color: var(--primary-color);
            background: linear-gradient(145deg, #ffffff, #f0f8ff);
        }

        .portal-card h4 {
            margin: 0;
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 700;
            z-index: 1;
            position: relative;
        }

        .dashboard-sub-page {
            display: none;
            margin-top: 2rem;
            background: var(--card-gradient);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: var(--shadow-large);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dashboard-sub-page.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        .back-to-portals {
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--info-color), #3182ce);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-medium);
            padding: 12px 24px;
            font-family: inherit;
        }

        .back-to-portals:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .search-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--border-color);
        }

        .search-filters label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .search-filters input,
        .search-filters select {
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            color: var(--text-primary);
            background: white;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .search-filters input:focus,
        .search-filters select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .import-export {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            padding: 1.5rem;
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--border-color);
        }

        .import-export input[type="file"] {
            padding: 12px 20px;
            border-radius: 10px;
            border: 2px solid var(--border-color);
            background: white;
            color: var(--text-primary);
            font-family: inherit;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .import-export input[type="file"]:hover {
            border-color: var(--primary-color);
        }

        #statement-print-area, #invoice-print-area {
            display: none;
            padding: 40px;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
            background: white;
            color: #333;
            max-width: 210mm;
            margin: 0 auto;
        }

        #statement-print-area h2, #invoice-print-area h2 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
            font-weight: 700;
        }

        #statement-print-area .statement-header, #invoice-print-area .statement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        #statement-print-area .statement-header h3, #invoice-print-area .statement-header h3 {
            color: #333;
            font-size: 1.5rem;
            margin: 0;
        }

        #statement-print-area .statement-header p, #invoice-print-area .statement-header p {
            color: #666;
            margin: 5px 0;
            font-size: 1rem;
        }

        #statement-print-area table, #invoice-print-area table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
        }

        #statement-print-area table th, #invoice-print-area table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 700;
            border: 1px solid #ddd;
        }

        #statement-print-area table td, #invoice-print-area table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
            background: white;
            color: #333;
        }

        #statement-print-area .statement-totals, #invoice-print-area .statement-totals {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #ddd;
            text-align: left;
        }

        #statement-print-area .statement-totals p, #invoice-print-area .statement-totals p {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 10px 0;
            color: #333;
        }

        #statement-print-area .statement-totals strong, #invoice-print-area .statement-totals strong {
            color: #667eea;
        }

        /* Enhanced Invoice Styles */
        .professional-invoice {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            color: #333;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid var(--primary-color);
        }

        .invoice-logo {
            text-align: right;
        }

        .invoice-logo h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 900;
            margin: 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .invoice-logo p {
            color: var(--text-secondary);
            margin: 5px 0;
            font-size: 1rem;
        }

        .invoice-number {
            text-align: left;
            color: var(--text-secondary);
        }

        .invoice-number h3 {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin: 0;
        }

        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .invoice-to, .invoice-from {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            border-right: 4px solid var(--primary-color);
        }

        .invoice-to h4, .invoice-from h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .invoice-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 30px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .invoice-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .invoice-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            background: white;
        }

        .invoice-table tr:nth-child(even) td {
            background: #f8fafc;
        }

        .invoice-table .total-row td {
            background: #edf2f7;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .invoice-summary {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 30px;
            margin-top: 30px;
        }

        .invoice-notes {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            border-right: 4px solid var(--info-color);
        }

        .invoice-notes h4 {
            color: var(--info-color);
            margin-bottom: 10px;
        }

        .invoice-totals {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid var(--primary-color);
            min-width: 300px;
        }

        .invoice-totals .total-line {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px dashed #e2e8f0;
        }

        .invoice-totals .total-line:last-child {
            border-bottom: none;
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary-color);
            background: #f0f8ff;
            padding: 15px;
            margin: 10px -20px -20px -20px;
            border-radius: 0 0 8px 8px;
        }

        .preview-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        #statement-print-area h2, #invoice-print-area h2 {
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
            font-size: 2rem;
            font-weight: 700;
        }

        #statement-print-area .statement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ddd;
        }
        #statement-print-area .statement-header div {
            flex: 1;
        }
        #statement-print-area .statement-header h3 {
            margin: 0;
            font-size: 1.5rem;
            color: var(--text-primary);
        }
        #statement-print-area .statement-header p {
            margin: 5px 0;
            font-size: 1rem;
            color: var(--text-secondary);
        }
        #statement-print-area .statement-totals {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 2px solid #ddd;
            text-align: left;
        }
        #statement-print-area .statement-totals p {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        #statement-print-area .statement-totals strong {
            color: var(--primary-color);
        }
        #statement-print-area table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        #statement-print-area table th, #statement-print-area table td {
             border: 1px solid #ddd;
             padding: 12px;
             text-align: center;
        }
        #statement-print-area table th {
            background-color: #f2f2f2;
            font-weight: 700;
        }

        #data-table-container {
            margin-top: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-medium);
            padding: 2rem;
            border: 1px solid var(--border-color);
            overflow-x: auto;
        }

        #data-table-container h3 {
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
            font-size: 1.8rem;
            font-weight: 700;
            position: relative;
        }

        #data-table-container h3::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -8px;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 1.5px;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 1rem;
            font-size: 0.95rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-light);
            background: white;
        }

        table th,
        table td {
            padding: 15px 12px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            word-wrap: break-word;
        }

        table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-weight: 700;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        table tbody tr {
            transition: all 0.3s ease;
        }

        table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        table tbody tr:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            transform: scale(1.01);
        }

        table td .edit-button,
        table td .delete-button {
            padding: 8px 16px;
            margin: 0 4px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        table td .edit-button {
            background: linear-gradient(135deg, var(--warning-color), #dd6b20);
            color: white;
        }

        table td .edit-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        table td .delete-button {
            background: linear-gradient(135deg, var(--danger-color), #e53e3e);
            color: white;
        }

        table td .delete-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .totals-container {
            text-align: right;
            margin-top: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 16px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
        }

        .totals-container p {
            margin: 1rem 0;
            font-size: 1.2rem;
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px dashed var(--border-color);
        }

        .totals-container p:last-child {
            border-bottom: none;
            font-weight: 700;
            font-size: 1.3rem;
        }

        .totals-container strong {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        tr.paid-expense {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 161, 105, 0.1)) !important;
        }

        tr.paid-expense td {
            color: var(--success-color);
            font-weight: 600;
        }

        tr.unpaid-expense {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1)) !important;
        }

        tr.unpaid-expense td {
            color: var(--danger-color);
            font-weight: 600;
        }

        tr.partial-expense {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1), rgba(221, 107, 32, 0.1)) !important;
        }

        tr.partial-expense td {
            color: var(--warning-color);
            font-weight: 600;
        }

        /* تصميم احترافي لبوابة موقف العمل */
        #dashboard-work-status-view {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 25px;
            padding: 40px;
            margin: 20px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        #dashboard-work-status-view::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        #dashboard-work-status-view h4 {
            color: #2d3748;
            font-size: 2.8rem;
            font-weight: 900;
            text-align: center;
            margin-bottom: 50px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        #dashboard-work-status-view h4::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        /* تصميم مربع البحث الاحترافي الجديد */
        .search-filters-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .search-filters-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        .search-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .search-header h5 {
            color: #2d3748;
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .search-header p {
            color: #718096;
            font-size: 1rem;
            font-weight: 500;
        }

        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }

        .search-card {
            background: white;
            border-radius: 18px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(102, 126, 234, 0.08);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
            border-color: rgba(102, 126, 234, 0.2);
        }

        .search-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            flex-shrink: 0;
        }

        .search-icon.status-icon {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .search-icon.date-icon {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .search-content {
            flex: 1;
        }

        .search-content label {
            display: block;
            color: #4a5568;
            font-weight: 700;
            font-size: 1rem;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .search-content input,
        .search-content select {
            width: 100%;
            padding: 15px 18px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f7fafc;
            color: #2d3748;
            font-weight: 500;
        }

        .search-content input:focus,
        .search-content select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: scale(1.02);
        }

        .search-content input::placeholder {
            color: #a0aec0;
            font-style: italic;
        }

        .search-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .search-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 32px;
            border: none;
            border-radius: 15px;
            font-weight: 700;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            min-width: 160px;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .search-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }

        .search-btn:hover::before {
            left: 100%;
        }

        .search-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .search-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .search-btn.primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.3);
        }

        .search-btn.secondary {
            background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
            color: white;
        }

        .search-btn.secondary:hover {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            box-shadow: 0 20px 50px rgba(113, 128, 150, 0.3);
        }

        .search-btn.tertiary {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .search-btn.tertiary:hover {
            background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%);
            box-shadow: 0 20px 50px rgba(237, 137, 54, 0.3);
        }

        .search-btn i {
            font-size: 1.1rem;
        }

        .search-btn span {
            font-weight: 700;
        }

        /* منطقة التصدير والاستيراد */
        .export-actions {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.08);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .export-group,
        .import-group {
            text-align: center;
        }

        .export-group h5,
        .import-group h5 {
            color: #4a5568;
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .export-buttons,
        .import-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .export-buttons button,
        .import-buttons button {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-weight: 700;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            min-width: 160px;
        }

        .export-buttons button:hover,
        .import-buttons button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .import-note {
            font-size: 0.9rem;
            color: #718096;
            margin: 0;
            padding: 10px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            border-left: 4px solid #667eea;
            text-align: right;
        }

        .import-note i {
            color: #667eea;
            margin-left: 5px;
        }

        /* تحسين منطقة المجاميع */
        .totals-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            color: white;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .totals-summary p {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .totals-summary span {
            display: block;
            font-size: 1.8rem;
            font-weight: 900;
            margin-top: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* تحسين الجدول */
        #dashboard-work-status-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        #dashboard-work-status-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #dashboard-work-status-table th {
            padding: 20px 15px;
            color: white;
            font-weight: 700;
            text-align: center;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            position: relative;
        }

        #dashboard-work-status-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
        }

        #dashboard-work-status-table td {
            padding: 18px 15px;
            text-align: center;
            border-bottom: 1px solid #f1f5f9;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }

        #dashboard-work-status-table tbody tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            transform: scale(1.01);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        #dashboard-work-status-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        #dashboard-work-status-table tbody tr:nth-child(even):hover {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }

        /* تحسين الأزرار */
        #dashboard-work-status-view .edit-button,
        #dashboard-work-status-view .delete-button {
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 3px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        #dashboard-work-status-view .edit-button {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        #dashboard-work-status-view .edit-button:hover {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
        }

        #dashboard-work-status-view .delete-button {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        #dashboard-work-status-view .delete-button:hover {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.3);
        }

        /* تحسين أزرار الفلتر */
        #apply-work-status-filter {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        #apply-work-status-filter:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        #clear-work-status-filter {
            background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
            color: white;
        }

        #clear-work-status-filter:hover {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        }

        #download-work-status-excel {
            background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
            color: white;
        }

        #download-work-status-excel:hover {
            background: linear-gradient(135deg, #319795 0%, #2c7a7b 100%);
        }

        #print-statement-button {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        #print-statement-button:hover {
            background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%);
        }

        /* تحسينات الاستجابة للبحث الجديد */
        @media (max-width: 1200px) {
            .search-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            #dashboard-work-status-view {
                padding: 20px;
                margin: 10px;
                border-radius: 15px;
            }

            #dashboard-work-status-view h4 {
                font-size: 2rem;
                margin-bottom: 30px;
            }

            .search-filters-container {
                padding: 25px;
                margin-bottom: 25px;
            }

            .search-header h5 {
                font-size: 1.5rem;
            }

            .search-header p {
                font-size: 0.9rem;
            }

            .search-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .search-card {
                padding: 20px;
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .search-icon {
                width: 45px;
                height: 45px;
                font-size: 1.1rem;
            }

            .search-actions {
                flex-direction: column;
                gap: 15px;
            }

            .search-btn {
                width: 100%;
                min-width: auto;
                padding: 14px 24px;
            }

            .export-actions {
                padding: 15px;
            }

            .export-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .export-buttons button {
                width: 100%;
                min-width: auto;
            }

            .totals-summary {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 20px;
            }

            #dashboard-work-status-table {
                font-size: 0.8rem;
            }

            #dashboard-work-status-table th,
            #dashboard-work-status-table td {
                padding: 10px 8px;
            }

            #dashboard-work-status-view .edit-button,
            #dashboard-work-status-view .delete-button {
                padding: 8px 15px;
                font-size: 0.8rem;
                margin: 2px;
            }
        }

        @media (max-width: 480px) {
            .search-filters-container {
                padding: 20px;
                border-radius: 15px;
            }

            .search-card {
                padding: 15px;
                border-radius: 12px;
            }

            .search-icon {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .search-content label {
                font-size: 0.9rem;
            }

            .search-content input,
            .search-content select {
                padding: 12px 15px;
                font-size: 0.9rem;
            }

            .search-btn {
                padding: 12px 20px;
                font-size: 0.9rem;
            }
        }

        /* تصميم احترافي لواجهة المصاريف */
        .expenses-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            color: white;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
        }

        .expenses-header h2 {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 10px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .page-description {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .expenses-form-section,
        .expenses-stats-section,
        .expenses-table-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .section-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f5f9;
        }

        .section-header h3 {
            color: #2d3748;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-header p {
            color: #718096;
            font-size: 1rem;
            font-weight: 500;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label i {
            margin-left: 8px;
            color: var(--primary-color);
        }

        /* بطاقات الإحصائيات */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            display: flex;
            align-items: center;
            gap: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f5f9;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }

        .stat-card.total::before {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.paid::before {
            background: linear-gradient(90deg, #48bb78 0%, #38a169 100%);
        }

        .stat-card.unpaid::before {
            background: linear-gradient(90deg, #f56565 0%, #e53e3e 100%);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
        }

        .stat-card.total .stat-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.paid .stat-icon {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-card.unpaid .stat-icon {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .stat-content h4 {
            color: #4a5568;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 900;
            color: #2d3748;
            display: inline-block;
        }

        .stat-currency {
            font-size: 1rem;
            color: #718096;
            font-weight: 600;
            margin-right: 5px;
        }

        /* تحسين جدول المصاريف */
        #expenses-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f5f9;
        }

        #expenses-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #expenses-table th {
            padding: 18px 15px;
            color: white;
            font-weight: 700;
            text-align: center;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
        }

        #expenses-table th i {
            margin-left: 8px;
        }

        #expenses-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #f1f5f9;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }

        #expenses-table tbody tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            transform: scale(1.01);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        #expenses-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        #expenses-table tbody tr:nth-child(even):hover {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }

        /* تصميم احترافي لصفحة الإعدادات */
        .settings-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            color: white;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
        }

        .settings-header h2 {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 10px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .settings-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .settings-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .settings-section .section-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f5f9;
        }

        .settings-section .section-header h3 {
            color: #2d3748;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .settings-section .section-header p {
            color: #718096;
            font-size: 1rem;
            font-weight: 500;
        }

        /* تحسين حقول الإدخال في الإعدادات */
        .settings-section input[type="text"],
        .settings-section input[type="color"],
        .settings-section select {
            padding: 15px 18px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            color: #2d3748;
            background: white;
            transition: all 0.3s ease;
            font-family: inherit;
            width: 100%;
        }

        .settings-section input[type="text"]:focus,
        .settings-section input[type="color"]:focus,
        .settings-section select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
            background: white;
            transform: translateY(-2px);
        }

        .settings-section input[type="color"] {
            height: 60px;
            padding: 5px;
            cursor: pointer;
        }

        .settings-section input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 8px;
        }

        .settings-section input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
            border: none;
            border-radius: 8px;
        }

        /* أزرار الإعدادات */
        .settings-actions {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .settings-actions::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #48bb78 0%, #38a169 100%);
        }

        .settings-actions .buttons-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            justify-items: center;
            max-width: 600px;
            margin: 0 auto;
        }

        .settings-actions .btn {
            padding: 15px 25px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            min-width: 200px;
            text-align: center;
        }

        .settings-actions .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* تحسينات متجاوبة للإعدادات */
        @media (max-width: 768px) {
            .settings-header {
                padding: 20px;
            }

            .settings-header h2 {
                font-size: 2rem;
            }

            .settings-section,
            .settings-actions {
                padding: 20px;
                margin-bottom: 20px;
            }

            .settings-actions .buttons-container {
                grid-template-columns: 1fr;
                gap: 15px;
                max-width: 100%;
            }

            .settings-actions .btn {
                min-width: 100%;
            }
        }

        /* تأثيرات الحركة المحسنة */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        #dashboard-work-status-view {
            animation: fadeInUp 0.6s ease-out;
        }

        .search-filters-container {
            animation: slideInRight 0.8s ease-out;
        }

        .search-card {
            animation: scaleIn 0.6s ease-out;
        }

        .search-card:nth-child(1) { animation-delay: 0.1s; }
        .search-card:nth-child(2) { animation-delay: 0.2s; }
        .search-card:nth-child(3) { animation-delay: 0.3s; }
        .search-card:nth-child(4) { animation-delay: 0.4s; }

        .search-btn {
            animation: bounceIn 0.8s ease-out;
        }

        .search-btn:nth-child(1) { animation-delay: 0.5s; }
        .search-btn:nth-child(2) { animation-delay: 0.6s; }
        .search-btn:nth-child(3) { animation-delay: 0.7s; }

        .export-actions {
            animation: fadeInUp 0.8s ease-out 0.3s both;
        }

        .totals-summary {
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        #dashboard-work-status-table {
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        /* تأثيرات التفاعل المتقدمة */
        .search-card:hover .search-icon {
            transform: rotate(10deg) scale(1.1);
        }

        .search-content input:focus + .search-icon,
        .search-content select:focus + .search-icon {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* تأثير التحميل للأزرار */
        .search-btn.loading {
            position: relative;
            color: transparent;
        }

        .search-btn.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين زر العودة */
        .back-to-portals {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .back-to-portals:hover {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .back-to-portals i {
            margin-left: 8px;
        }

        #dashboard-settings .form-container,
        #dashboard-credentials .form-container {
            grid-template-columns: 1fr;
            max-width: 800px;
            margin: 2rem auto;
        }

        #dashboard-settings .form-group input[type="color"] {
            height: 60px;
            padding: 8px;
            border-radius: 12px;
            cursor: pointer;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            width: 100%;
            border: 2px solid var(--border-color);
        }

        #dashboard-settings .form-group input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }
        #dashboard-settings .form-group input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 8px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-gradient);
            padding: 2.5rem;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            box-shadow: var(--shadow-large);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            animation: modalFadeIn 0.4s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .close-button {
            position: absolute;
            top: 1rem;
            left: 1.5rem;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(113, 128, 150, 0.1);
        }

        .close-button:hover {
            color: var(--danger-color);
            background: rgba(245, 101, 101, 0.1);
            transform: rotate(90deg);
        }

        /* أنماط المعاينة - تطابق الطباعة تماماً */
        .preview-content {
            background: white !important;
            color: #333 !important;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
            padding: 40px;
            max-width: 210mm;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            unicode-bidi: embed;
        }

        .preview-content h2 {
            color: #667eea !important;
            background: none !important;
            -webkit-text-fill-color: #667eea !important;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
            font-weight: 700;
        }

        .preview-content .statement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
            direction: rtl;
            text-align: right;
        }

        .preview-content .statement-header h3 {
            color: #333 !important;
            background: none !important;
            -webkit-text-fill-color: #333 !important;
            font-size: 1.5rem;
            margin: 0;
        }

        .preview-content .statement-header p {
            color: #666 !important;
            margin: 5px 0;
            font-size: 1rem;
        }

        .preview-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            direction: rtl;
            text-align: right;
        }

        .preview-content table th {
            background: #667eea !important;
            color: white !important;
            padding: 15px;
            text-align: center;
            font-weight: 700;
            border: 1px solid #ddd;
        }

        .preview-content table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
            background: white !important;
            color: #333 !important;
        }

        .preview-content .statement-totals {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #ddd;
            text-align: right;
            direction: rtl;
        }

        .preview-content .statement-totals p {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 10px 0;
            color: #333 !important;
        }

        .preview-content .statement-totals strong {
            color: #667eea !important;
        }

        @media print {
            body * {
                visibility: hidden;
            }
            #invoice-print-area, #invoice-print-area *,
            #statement-print-area, #statement-print-area *,
            .print-invoice, .print-invoice * {
                visibility: visible;
            }
            #invoice-print-area, #statement-print-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 210mm;
                min-height: 297mm;
                box-sizing: border-box;
                padding: 15mm;
                display: block;
                background: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            #statement-print-area {
                display: block;
            }
            .print-invoice {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                background: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            @page {
                size: A4;
                margin: 15mm;
            }

            /* Hide preview modal during print */
            #preview-modal {
                display: none !important;
            }

            /* Ensure colors print correctly */
            .print-table th,
            .invoice-table th {
                background: #667eea !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .print-header,
            .invoice-header {
                border-bottom: 3px solid #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        /* Responsive Design for Invoice Preview */
        @media (max-width: 768px) {
            .professional-invoice {
                padding: 20px;
                margin: 10px;
            }

            .invoice-header {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .invoice-logo h1 {
                font-size: 2rem;
            }

            .invoice-details {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .invoice-table {
                font-size: 0.9rem;
            }

            .invoice-table th,
            .invoice-table td {
                padding: 10px 5px;
            }

            .invoice-summary {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .invoice-totals {
                min-width: auto;
            }

            .preview-actions {
                flex-direction: column;
                gap: 10px;
            }

            .preview-actions .btn {
                width: 100%;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            nav ul {
                flex-direction: column;
                gap: 1rem;
                padding: 0 1rem;
            }

            nav ul li a {
                font-size: 1rem;
                padding: 10px 20px;
                display: block;
                text-align: center;
            }

            header h1 {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }

            .form-container {
                grid-template-columns: 1fr;
                padding: 1.5rem;
                gap: 1rem;
            }

            .buttons-container {
                flex-direction: column;
                gap: 0.75rem;
            }

            .buttons-container .btn {
                width: 100%;
                justify-content: center;
                padding: 12px 20px;
            }

            .login-container {
                width: 95%;
                padding: 2rem;
                margin: 1rem auto;
            }

            .modal-content {
                width: 95%;
                padding: 2rem;
                margin: 1rem;
            }

            .portal-card {
                padding: 2rem 1.5rem;
            }

            .dashboard-portals {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .search-filters,
            .import-export {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .import-export {
                flex-direction: column;
                align-items: stretch;
            }

            .import-export .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            main {
                padding: 1rem;
            }

            .page {
                padding: 1.5rem;
                margin-bottom: 1rem;
            }

            .dashboard-section {
                padding: 1.5rem;
            }

            .dashboard-sub-page {
                padding: 1.5rem;
            }

            #data-table-container {
                padding: 1rem;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            table {
                font-size: 0.8rem;
                min-width: 800px;
            }

            table th,
            table td {
                padding: 8px 6px;
                white-space: nowrap;
            }

            table td .edit-button,
            table td .delete-button {
                padding: 6px 12px;
                font-size: 0.8rem;
                margin: 2px;
                display: block;
                width: 100%;
            }

            table td .delete-button {
                margin-top: 0.5rem;
            }

            .totals-container {
                padding: 1rem;
                font-size: 1rem;
            }

            .totals-container p {
                font-size: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            h2 {
                font-size: 2rem;
                margin-bottom: 2rem;
            }

            .dashboard-section h3 {
                font-size: 1.8rem;
            }

            .dashboard-section h4 {
                font-size: 1.4rem;
            }

            .form-group input,
            .form-group select,
            .form-group textarea {
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .search-filters input,
            .search-filters select {
                font-size: 16px;
            }

            /* تحسينات متجاوبة لحقل اسم المستفيد */
            #beneficiary-name {
                font-size: 16px !important;
                padding: 18px 20px !important;
                min-height: 55px !important;
            }

            .suggestions-list {
                max-height: 150px;
            }

            .suggestion-item {
                padding: 12px 15px;
                font-size: 15px;
            }

            /* تحسينات متجاوبة للتصدير والاستيراد */
            .export-actions {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .export-buttons,
            .import-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .export-buttons button,
            .import-buttons button {
                min-width: 100%;
                padding: 12px 20px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            header {
                padding: 1rem 0;
            }

            header h1 {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            nav ul li a {
                font-size: 0.9rem;
                padding: 8px 16px;
            }

            h2 {
                font-size: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .dashboard-section h3 {
                font-size: 1.4rem;
            }

            .dashboard-section h4 {
                font-size: 1.2rem;
            }

            .portal-card {
                padding: 1.5rem 1rem;
            }

            .portal-card h4 {
                font-size: 1.1rem;
            }

            .btn {
                padding: 10px 16px;
                font-size: 0.9rem;
            }

            .form-container {
                padding: 1rem;
            }

            .login-container {
                padding: 1.5rem;
                width: 98%;
            }

            /* تحسينات إضافية لحقل اسم المستفيد على الشاشات الصغيرة */
            #beneficiary-name {
                font-size: 16px !important;
                padding: 15px 18px !important;
                min-height: 50px !important;
            }

            .suggestions-list {
                max-height: 120px;
                border-radius: 0 0 8px 8px;
            }

            .suggestion-item {
                padding: 10px 12px;
                font-size: 14px;
            }

            /* تحسينات متجاوبة لواجهة المصاريف */
            .expenses-header {
                padding: 20px;
            }

            .expenses-header h2 {
                font-size: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .stat-card {
                padding: 20px;
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 1.3rem;
            }

            .expenses-form-section,
            .expenses-stats-section,
            .expenses-table-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            #expenses-table {
                font-size: 0.8rem;
            }

            #expenses-table th,
            #expenses-table td {
                padding: 10px 8px;
            }

            .modal-content {
                padding: 1.5rem;
                width: 98%;
            }

            table {
                font-size: 0.7rem;
            }

            table th,
            table td {
                padding: 6px 4px;
            }

            .totals-container {
                padding: 0.75rem;
            }

            .totals-container p {
                font-size: 0.9rem;
            }

            .page {
                padding: 1rem;
            }

            .dashboard-section,
            .dashboard-sub-page {
                padding: 1rem;
            }

            #data-table-container {
                padding: 0.75rem;
            }

            .back-to-portals {
                font-size: 0.9rem;
                padding: 10px 20px;
            }

            .close-button {
                top: 0.5rem;
                left: 1rem;
                font-size: 1.5rem;
                width: 35px;
                height: 35px;
            }
        }

        /* Landscape orientation for mobile devices */
        @media (max-width: 768px) and (orientation: landscape) {
            header {
                padding: 0.5rem 0;
            }

            header h1 {
                font-size: 1.5rem;
            }

            nav ul {
                flex-direction: row;
                gap: 0.5rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            nav ul li a {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .page {
                padding: 1rem;
            }
        }

        /* Touch-friendly improvements */
        @media (pointer: coarse) {
            .btn {
                min-height: 44px;
                min-width: 44px;
            }

            table td .edit-button,
            table td .delete-button {
                min-height: 40px;
                min-width: 80px;
            }

            .portal-card {
                min-height: 120px;
                cursor: pointer;
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
            }

            input, select, textarea {
                min-height: 44px;
            }

            .close-button {
                min-width: 44px;
                min-height: 44px;
            }
        }

        /* Print styles improvement for mobile */
        @media print {
            body {
                font-size: 12px;
            }

            table {
                font-size: 10px;
            }

            .statement-header h3 {
                font-size: 16px;
            }

            .statement-totals p {
                font-size: 12px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <h1>مدير المشاريع</h1>
        <nav>
            <ul>
                <li><a href="#work-status" id="work-status-link">موقف العمل</a></li>
                <li><a href="#expenses" id="expenses-link">المصاريف</a></li>
                <li><a href="#dashboard" id="dashboard-link">المدخل السري</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="work-status" class="page active">
            <h2>موقف العمل</h2>
            <div class="form-container">
                <div class="form-group">
                    <label for="date">التاريخ:</label>
                    <input type="date" id="date" name="date">
                </div>
                <div class="form-group">
                    <label for="quantity">الكمية:</label>
                    <input type="number" id="quantity" name="quantity">
                </div>
                <div class="form-group">
                    <label for="cement-type-text">السمنت:</label>
                    <input type="text" id="cement-type-text" name="cement-type-text">
                </div>
                <div class="form-group">
                    <label for="price-per-meter">سعر المتر:</label>
                    <input type="number" id="price-per-meter" name="price-per-meter">
                </div>
                <div class="form-group">
                    <label for="pump-wage">أجرة البم:</label>
                    <input type="number" id="pump-wage" name="pump-wage">
                </div>
                <div class="form-group">
                    <label for="total-amount">المبلغ:</label>
                    <input type="text" id="total-amount" name="total-amount" readonly>
                </div>
                <div class="form-group">
                    <label for="received-amount">المبلغ الواصل:</label>
                    <input type="number" id="received-amount" name="received-amount">
                </div>
                <div class="form-group">
                    <label for="cement-type-dropdown">نوع السمنت:</label>
                    <select id="cement-type-dropdown" name="cement-type-dropdown">
                        <option value="">اختر نوع السمنت</option>
                        <option value="عادي">عادي</option>
                        <option value="مقاوم">مقاوم</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="resistance">المقاومة:</label>
                    <select id="resistance" name="resistance">
                        <option value="">اختر المقاومة</option>
                        <option value="250">250</option>
                        <option value="300">300</option>
                        <option value="350">350</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="beneficiary-name">اسم المستفيد:</label>
                    <div class="autocomplete-container">
                        <input type="text" id="beneficiary-name" name="beneficiary-name" autocomplete="off" placeholder="ابدأ بكتابة اسم المستفيد...">
                        <div id="beneficiary-suggestions" class="suggestions-list"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="details">التفاصيل:</label>
                    <textarea id="details" name="details"></textarea>
                </div>
                <div class="form-group">
                    <label for="added-by">المادة المضافة:</label>
                    <input type="text" id="added-by" name="added-by" placeholder="مثال: إضافات خاصة، مواد إضافية">
                </div>
                <div class="form-group">
                    <label for="employee-name">اسم الموظف:</label>
                    <select id="employee-name" name="employee-name">
                        <option value="">اختر اسم الموظف</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="payment-date">تاريخ التسديد:</label>
                    <input type="date" id="payment-date" name="payment-date">
                </div>
                <div class="form-group">
                    <label for="phone-number">اختيار جهة الاتصال:</label>
                    <select id="phone-number" name="phone-number">
                        <option value="">اختر اسم أو أدخل رقم جديد</option>
                    </select>
                    <input type="text" id="new-phone-number" name="new-phone-number" placeholder="رقم الهاتف">
                </div>
                <div class="form-group">
                    <label for="notes">الملاحظات:</label>
                    <textarea id="notes" name="notes"></textarea>
                </div>
                <div class="buttons-container">
                    <button id="preview-button" class="btn btn-info"><i class="fas fa-search"></i> معاينة قبل الإضافة</button>
                    <button id="add-data-button" class="btn btn-primary"><i class="fas fa-plus-circle"></i> إضافةニニ</button>
                    <button id="clear-button" class="btn btn-secondary"><i class="fas fa-eraser"></i> مسح</button>
                    <button id="whatsapp-button" class="btn btn-success"><i class="fab fa-whatsapp"></i> إرسال على واتساب</button>
                    <button id="print-invoice-button" class="btn btn-warning"><i class="fas fa-print"></i> طباعة الفاتورة</button>
                </div>
            </div>
            <div id="invoice-print-area">
                <!-- Invoice content will be dynamically inserted here for printing -->
            </div>
            <div id="statement-print-area">
                <!-- Statement content will be dynamically inserted here for printing -->
            </div>

            <!-- Preview Modal -->
            <div id="preview-modal" class="modal">
                <div class="modal-content" style="max-width: 900px;">
                    <span class="close-button" id="preview-modal-close">&times;</span>
                    <div id="preview-modal-body">
                        <!-- Preview content will be injected here -->
                    </div>
                </div>
            </div>
        </section>

        <section id="expenses" class="page">
            <!-- Header Section -->
            <div class="expenses-header">
                <h2><i class="fas fa-money-bill-wave"></i> إدارة المصاريف</h2>
                <p class="page-description">تتبع وإدارة جميع المصاريف والنفقات بطريقة احترافية ومنظمة</p>
            </div>

            <!-- Form Section -->
            <div class="expenses-form-section">
                <div class="section-header">
                    <h3><i class="fas fa-plus-circle"></i> إضافة مصروف جديد</h3>
                    <p>املأ البيانات أدناه لإضافة مصروف جديد</p>
                </div>

                <div class="form-container">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expense-date"><i class="fas fa-calendar-alt"></i> التاريخ:</label>
                            <input type="date" id="expense-date" name="expense-date">
                        </div>
                        <div class="form-group">
                            <label for="expense-amount"><i class="fas fa-coins"></i> المبلغ (بالدينار العراقي):</label>
                            <input type="number" id="expense-amount" name="expense-amount" placeholder="أدخل المبلغ...">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="expense-source"><i class="fas fa-building"></i> جهة الصرف:</label>
                            <input type="text" id="expense-source" name="expense-source" placeholder="أدخل جهة الصرف...">
                        </div>
                        <div class="form-group">
                            <label for="expense-status"><i class="fas fa-check-circle"></i> الحالة:</label>
                            <select id="expense-status" name="expense-status">
                                <option value="غير مسدد">❌ غير مسدد</option>
                                <option value="مسدد">✅ مسدد</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="expense-details"><i class="fas fa-file-alt"></i> التفاصيل:</label>
                        <textarea id="expense-details" name="expense-details" placeholder="أدخل تفاصيل المصروف..."></textarea>
                    </div>

                    <div class="buttons-container">
                        <button id="add-expense-button" class="btn btn-primary">
                            <i class="fas fa-plus-circle"></i> إضافة مصروف
                        </button>
                        <button id="clear-expense-button" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i> مسح الحقول
                        </button>
                        <button id="export-expenses-button" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                    </div>
                </div>
            </div>
            <!-- Statistics Section -->
            <div class="expenses-stats-section">
                <div class="section-header">
                    <h3><i class="fas fa-chart-pie"></i> إحصائيات المصاريف</h3>
                    <p>ملخص شامل لجميع المصاريف والنفقات</p>
                </div>

                <div class="stats-cards">
                    <div class="stat-card total">
                        <div class="stat-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="stat-content">
                            <h4>إجمالي المصاريف</h4>
                            <span id="total-expenses" class="stat-value">0.00</span>
                            <span class="stat-currency">د.ع</span>
                        </div>
                    </div>

                    <div class="stat-card paid">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>المبلغ المسدد</h4>
                            <span id="paid-expenses" class="stat-value">0.00</span>
                            <span class="stat-currency">د.ع</span>
                        </div>
                    </div>

                    <div class="stat-card unpaid">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h4>المبلغ غير المسدد</h4>
                            <span id="unpaid-expenses" class="stat-value">0.00</span>
                            <span class="stat-currency">د.ع</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table Section -->
            <div class="expenses-table-section">
                <div class="section-header">
                    <h3><i class="fas fa-table"></i> قائمة المصاريف</h3>
                    <p>جميع المصاريف المسجلة مع إمكانية التعديل والحذف</p>
                </div>

                <div id="data-table-container">
                    <table id="expenses-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-cogs"></i> الإجراءات</th>
                                <th><i class="fas fa-calendar-alt"></i> التاريخ</th>
                                <th><i class="fas fa-coins"></i> المبلغ (د.ع)</th>
                                <th><i class="fas fa-file-alt"></i> التفاصيل</th>
                                <th><i class="fas fa-building"></i> جهة الصرف</th>
                                <th><i class="fas fa-check-circle"></i> الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
            <div id="edit-modal" class="modal">
                <div class="modal-content">
                    <span class="close-button" id="modal-close-button">&times;</span>
                    <div id="modal-body">
                        <!-- Form for editing will be injected here -->
                    </div>
                </div>
            </div>
        </section>

        <section id="dashboard" class="page">
            <h2>المدخل السري</h2>
            <div class="login-container">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password">
                <button id="login-button" class="btn btn-primary"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</button>
                <p id="login-message"></p>
            </div>
            <div id="dashboard-content" style="display: none;">
                <div id="dashboard-portals-view" class="dashboard-section">
                    <h3>بوابات لوحة التحكم</h3>
                    <div class="dashboard-portals">
                        <div class="portal-card" data-portal="credentials">
                            <h4>حسابات الدخول</h4>
                        </div>
                        <div class="portal-card" data-portal="contacts">
                            <h4>جهات الاتصال</h4>
                        </div>
                        <div class="portal-card" data-portal="employees">
                            <h4>الموظفين</h4>
                        </div>
                        <div class="portal-card" data-portal="work-status-dashboard">
                            <h4>موقف العمل</h4>
                        </div>
                        <div class="portal-card" data-portal="expenses-dashboard">
                            <h4>المصاريف</h4>
                        </div>
                        <div class="portal-card" data-portal="settings">
                            <h4>الإعدادات</h4>
                        </div>
                    </div>
                    <button id="logout-button" class="btn btn-danger" style="margin-top: 30px;"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button>
                </div>

                <div id="dashboard-credentials" class="dashboard-sub-page">
                    <button class="back-to-portals"><i class="fas fa-arrow-right"></i> العودة إلى البوابات</button>
                    <h4>حسابات الدخول</h4>
                    <div class="form-container">
                        <div class="form-group">
                            <label for="current-username-cred">اسم المستخدم الحالي:</label>
                            <input type="text" id="current-username-cred" name="current-username-cred" readonly>
                        </div>
                        <div class="form-group">
                            <label for="current-password-cred">كلمة المرور الحالية:</label>
                            <input type="password" id="current-password-cred" name="current-password-cred" readonly>
                        </div>
                        <div class="buttons-container">
                            <button id="update-credentials-button-portal" class="btn btn-info"><i class="fas fa-sync-alt"></i> تحديث بيانات الدخول</button>
                            <p id="credentials-message-portal"></p>
                        </div>
                    </div>
                    <h4>إضافة حساب جديد</h4>
                    <div class="form-container">
                        <div class="form-group">
                            <label for="add-username-cred">اسم المستخدم الجديد:</label>
                            <input type="text" id="add-username-cred" name="add-username-cred">
                        </div>
                        <div class="form-group">
                            <label for="add-password-cred">كلمة المرور الجديدة:</label>
                            <input type="password" id="add-password-cred" name="add-password-cred">
                        </div>
                        <div class="buttons-container">
                            <button id="add-credentials-button-portal" class="btn btn-success"><i class="fas fa-user-plus"></i> إضافة حساب</button>
                            <p id="add-credentials-message-portal"></p>
                        </div>
                    </div>
                    <div id="data-table-container">
                        <h3>قائمة الحسابات</h3>
                        <table id="credentials-table">
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>كلمة المرور</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="credentials-table-body">
                            </tbody>
                        </table>
                    </div>
                    <div id="edit-modal" class="modal">
                        <div class="modal-content">
                            <span class="close-button" id="modal-close-button">&times;</span>
                            <div id="modal-body">
                                <!-- Form for editing will be injected here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div id="dashboard-contacts" class="dashboard-sub-page">
                    <button class="back-to-portals"><i class="fas fa-arrow-right"></i> العودة إلى البوابات</button>
                    <h4>جهات الاتصال</h4>
                    <div class="form-container">
                        <div class="form-group">
                            <label for="contact-name">اسم الشخص:</label>
                            <input type="text" id="contact-name" name="contact-name">
                        </div>
                        <div class="form-group">
                            <label for="contact-phone">رقم الهاتف:</label>
                            <input type="text" id="contact-phone" name="contact-phone">
                        </div>
                        <div class="buttons-container">
                            <button id="add-contact-button" class="btn btn-primary"><i class="fas fa-plus-square"></i> إضافة جهة اتصال</button>
                            <button id="clear-contact-form" class="btn btn-secondary"><i class="fas fa-eraser"></i> مسح</button>
                        </div>
                    </div>
                    <div id="data-table-container">
                        <h3>جهات الاتصال المحفوظة</h3>
                        <table id="phone-numbers-table">
                            <thead>
                                <tr>
                                    <th>اسم الشخص</th>
                                    <th>رقم الهاتف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="phone-numbers-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="dashboard-employees" class="dashboard-sub-page">
                    <button class="back-to-portals"><i class="fas fa-arrow-right"></i> العودة إلى البوابات</button>
                    <h4>إدارة الموظفين</h4>
                    <div class="form-container">
                        <div class="form-group">
                            <label for="employee-name-input">اسم الموظف:</label>
                            <input type="text" id="employee-name-input" name="employee-name-input">
                        </div>
                        <div class="form-group">
                            <label for="employee-phone-input">رقم الهاتف:</label>
                            <input type="text" id="employee-phone-input" name="employee-phone-input">
                        </div>
                        <div class="form-group">
                            <label for="employee-position">المنصب:</label>
                            <input type="text" id="employee-position" name="employee-position" placeholder="مثال: مهندس، مشرف، عامل">
                        </div>
                        <div class="buttons-container">
                            <button id="add-employee-button" class="btn btn-success"><i class="fas fa-user-plus"></i> إضافة موظف</button>
                            <button id="clear-employee-button" class="btn btn-secondary"><i class="fas fa-eraser"></i> مسح</button>
                        </div>
                    </div>
                    <div id="data-table-container">
                        <h3>قائمة الموظفين</h3>
                        <table id="employees-table">
                            <thead>
                                <tr>
                                    <th>اسم الموظف</th>
                                    <th>رقم الهاتف</th>
                                    <th>المنصب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="employees-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="dashboard-work-status-view" class="dashboard-sub-page">
                    <button class="back-to-portals"><i class="fas fa-arrow-right"></i> العودة إلى البوابات</button>
                    <h4>موقف العمل</h4>
                    <div class="search-filters-container">
                        <div class="search-header">
                            <h5><i class="fas fa-search"></i> البحث والفلترة</h5>
                            <p>استخدم الحقول أدناه للبحث وفلترة البيانات</p>
                        </div>

                        <div class="search-grid">
                            <div class="search-card">
                                <div class="search-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="search-content">
                                    <label for="search-beneficiary">البحث بالاسم</label>
                                    <div class="autocomplete-container">
                                        <input type="text" id="search-beneficiary" placeholder="أدخل اسم المستفيد..." autocomplete="off">
                                        <div id="search-beneficiary-suggestions" class="suggestions-list"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="search-card">
                                <div class="search-icon status-icon">
                                    <i class="fas fa-money-check-alt"></i>
                                </div>
                                <div class="search-content">
                                    <label for="search-paid-status">حالة السداد</label>
                                    <select id="search-paid-status">
                                        <option value="">جميع الحالات</option>
                                        <option value="paid">✅ مسدد بالكامل</option>
                                        <option value="unpaid">❌ غير مسدد</option>
                                        <option value="partial">⚠️ مسدد جزئياً</option>
                                    </select>
                                </div>
                            </div>

                            <div class="search-card">
                                <div class="search-icon date-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="search-content">
                                    <label for="search-date-from">التاريخ من</label>
                                    <input type="date" id="search-date-from">
                                </div>
                            </div>

                            <div class="search-card">
                                <div class="search-icon date-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="search-content">
                                    <label for="search-date-to">التاريخ إلى</label>
                                    <input type="date" id="search-date-to">
                                </div>
                            </div>
                        </div>

                        <div class="search-actions">
                            <button id="apply-work-status-filter" class="search-btn primary">
                                <i class="fas fa-filter"></i>
                                <span>تطبيق البحث</span>
                            </button>
                            <button id="clear-work-status-filter" class="search-btn secondary">
                                <i class="fas fa-eraser"></i>
                                <span>مسح الفلاتر</span>
                            </button>
                            <button id="reset-all-filters" class="search-btn tertiary">
                                <i class="fas fa-undo"></i>
                                <span>إعادة تعيين</span>
                            </button>
                        </div>
                    </div>

                    <div class="export-actions">
                        <div class="export-group">
                            <h5>📊 تصدير البيانات</h5>
                            <div class="export-buttons">
                                <button id="download-work-status-excel" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> تحميل Excel
                                </button>
                                <button id="print-statement-button" class="btn btn-info">
                                    <i class="fas fa-print"></i> طباعة كشف الحساب
                                </button>
                            </div>
                        </div>

                        <div class="import-group">
                            <h5>📥 استيراد البيانات</h5>
                            <div class="import-buttons">
                                <button id="import-work-status-button" class="btn btn-warning">
                                    <i class="fas fa-file-upload"></i> استيراد من Excel
                                </button>
                                <input type="file" id="import-work-status-excel" accept=".xlsx, .xls" style="display: none;">
                            </div>
                            <p class="import-note">
                                <i class="fas fa-info-circle"></i>
                                يمكنك استيراد ملف Excel يحتوي على بيانات موقف العمل
                            </p>
                        </div>
                    </div>
                    <div class="totals-summary">
                        <p><strong>مجموع المبالغ</strong><span id="total-amount-sum">0.00</span></p>
                        <p><strong>مجموع الواصل</strong><span id="total-received-sum">0.00</span></p>
                        <p><strong>مجموع المتبقي</strong><span id="total-remaining-sum">0.00</span></p>
                    </div>
                    <div id="data-table-container">
                        <table id="dashboard-work-status-table">
                            <thead>
                                <tr>
                                    <th style="background: linear-gradient(135deg, #48bb78, #38a169); color: white;">الإجراءات</th>
                                    <th>التاريخ</th>
                                    <th>الكمية</th>
                                    <th>السمنت</th>
                                    <th>سعر المتر</th>
                                    <th>أجرة البم</th>
                                    <th>المبلغ</th>
                                    <th>المبلغ الواصل</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>نوع السمنت</th>
                                    <th>المقاومة</th>
                                    <th>اسم المستفيد</th>
                                    <th>التفاصيل</th>
                                    <th>المادة المضافة</th>
                                    <th>اسم الموظف</th>
                                    <th>تاريخ التسديد</th>
                                    <th>رقم الهاتف</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="dashboard-work-status-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="dashboard-expenses-view" class="dashboard-sub-page">
                    <button class="back-to-portals"><i class="fas fa-arrow-right"></i> العودة إلى البوابات</button>
                    <h4>المصاريف</h4>
                    <div class="search-filters">
                        <label for="search-expense-source">بحث بجهة الصرف:</label>
                        <div class="autocomplete-container">
                            <input type="text" id="search-expense-source" placeholder="جهة الصرف" autocomplete="off">
                            <div id="search-expense-source-suggestions" class="suggestions-list"></div>
                        </div>

                        <label for="search-expense-date-from">التاريخ من:</label>
                        <input type="date" id="search-expense-date-from">

                        <label for="search-expense-date-to">التاريخ إلى:</label>
                        <input type="date" id="search-expense-date-to">

                        <button id="apply-expense-filter" class="btn btn-primary"><i class="fas fa-filter"></i> تطبيق البحث</button>
                        <button id="clear-expense-filter" class="btn btn-secondary"><i class="fas fa-times-circle"></i> مسح البحث</button>
                    </div>
                    <div class="totals-container">
                        <p><strong>مجموع المصاريف:</strong> <span id="dashboard-total-expenses">0.00</span> د.ع</p>
                    </div>
                    <div id="data-table-container">
                        <table id="dashboard-expenses-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ (د.ع)</th>
                                    <th>التفاصيل</th>
                                    <th>جهة الصرف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="dashboard-expenses-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="dashboard-settings" class="dashboard-sub-page">
                    <button class="back-to-portals"><i class="fas fa-arrow-right"></i> العودة إلى البوابات</button>

                    <!-- Header Section -->
                    <div class="settings-header">
                        <h2><i class="fas fa-cogs"></i> إعدادات النظام</h2>
                        <p class="page-description">تخصيص وتكوين جميع جوانب النظام حسب احتياجاتك</p>
                    </div>

                    <!-- Colors Settings -->
                    <div class="settings-section">
                        <div class="section-header">
                            <h3><i class="fas fa-palette"></i> إعدادات الألوان</h3>
                            <p>تخصيص ألوان النظام والواجهة</p>
                        </div>

                        <div class="form-container">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="primary-color-setting"><i class="fas fa-circle" style="color: #667eea;"></i> اللون الأساسي:</label>
                                    <input type="color" id="primary-color-setting" name="primary-color-setting" value="#667eea">
                                </div>
                                <div class="form-group">
                                    <label for="secondary-color-setting"><i class="fas fa-circle" style="color: #764ba2;"></i> اللون الثانوي:</label>
                                    <input type="color" id="secondary-color-setting" name="secondary-color-setting" value="#764ba2">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="header-bg-color-setting"><i class="fas fa-header"></i> لون خلفية الهيدر:</label>
                                    <input type="color" id="header-bg-color-setting" name="header-bg-color-setting" value="#667eea">
                                </div>
                                <div class="form-group">
                                    <label for="button-bg-color-setting"><i class="fas fa-mouse-pointer"></i> لون خلفية الأزرار:</label>
                                    <input type="color" id="button-bg-color-setting" name="button-bg-color-setting" value="#667eea">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="success-color-setting"><i class="fas fa-check-circle" style="color: #48bb78;"></i> لون النجاح:</label>
                                    <input type="color" id="success-color-setting" name="success-color-setting" value="#48bb78">
                                </div>
                                <div class="form-group">
                                    <label for="danger-color-setting"><i class="fas fa-exclamation-circle" style="color: #f56565;"></i> لون الخطر:</label>
                                    <input type="color" id="danger-color-setting" name="danger-color-setting" value="#f56565">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Font Settings -->
                    <div class="settings-section">
                        <div class="section-header">
                            <h3><i class="fas fa-font"></i> إعدادات الخط</h3>
                            <p>تخصيص نوع وحجم الخطوط في النظام</p>
                        </div>

                        <div class="form-container">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="font-family-setting"><i class="fas fa-text-height"></i> نوع الخط:</label>
                                    <select id="font-family-setting" name="font-family-setting">
                                        <option value="'Tajawal', sans-serif">🔤 Tajawal (الافتراضي)</option>
                                        <option value="'Amiri', serif">📖 Amiri</option>
                                        <option value="'Cairo', sans-serif">🏛️ Cairo</option>
                                        <option value="'Almarai', sans-serif">✨ Almarai</option>
                                        <option value="Arial, sans-serif">🔠 Arial</option>
                                        <option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">💻 Segoe UI</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="font-size-setting"><i class="fas fa-text-width"></i> حجم الخط الأساسي:</label>
                                    <select id="font-size-setting" name="font-size-setting">
                                        <option value="14px">📏 صغير (14px)</option>
                                        <option value="16px" selected>📐 متوسط (16px)</option>
                                        <option value="18px">📏 كبير (18px)</option>
                                        <option value="20px">📐 كبير جداً (20px)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="header-font-size-setting"><i class="fas fa-heading"></i> حجم خط العناوين:</label>
                                    <select id="header-font-size-setting" name="header-font-size-setting">
                                        <option value="2rem">🔤 صغير</option>
                                        <option value="2.5rem" selected>🔠 متوسط</option>
                                        <option value="3rem">📝 كبير</option>
                                        <option value="3.5rem">📋 كبير جداً</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- General Settings -->
                    <div class="settings-section">
                        <div class="section-header">
                            <h3><i class="fas fa-globe"></i> الإعدادات العامة</h3>
                            <p>إعدادات أساسية للنظام والعناوين</p>
                        </div>

                        <div class="form-container">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="site-title-setting"><i class="fas fa-home"></i> عنوان الموقع:</label>
                                    <input type="text" id="site-title-setting" name="site-title-setting" placeholder="مدير المشاريع">
                                </div>
                                <div class="form-group">
                                    <label for="work-status-title-setting"><i class="fas fa-tasks"></i> عنوان صفحة موقف العمل:</label>
                                    <input type="text" id="work-status-title-setting" name="work-status-title-setting" value="موقف العمل">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="expenses-title-setting"><i class="fas fa-money-bill-wave"></i> عنوان صفحة المصاريف:</label>
                                    <input type="text" id="expenses-title-setting" name="expenses-title-setting" value="المصاريف">
                                </div>
                                <div class="form-group">
                                    <label for="dashboard-title-setting"><i class="fas fa-lock"></i> عنوان المدخل السري:</label>
                                    <input type="text" id="dashboard-title-setting" name="dashboard-title-setting" value="المدخل السري">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Field Labels Settings -->
                    <div class="settings-section">
                        <div class="section-header">
                            <h3><i class="fas fa-tags"></i> تسميات الحقول - موقف العمل</h3>
                            <p>تخصيص أسماء وتسميات حقول الإدخال</p>
                        </div>

                        <div class="form-container">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="date-label-setting"><i class="fas fa-calendar-alt"></i> تسمية حقل التاريخ:</label>
                                    <input type="text" id="date-label-setting" name="date-label-setting" value="التاريخ:">
                                </div>
                                <div class="form-group">
                                    <label for="quantity-label-setting"><i class="fas fa-cubes"></i> تسمية حقل الكمية:</label>
                                    <input type="text" id="quantity-label-setting" name="quantity-label-setting" value="الكمية:">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="cement-type-label-setting"><i class="fas fa-industry"></i> تسمية حقل السمنت:</label>
                                    <input type="text" id="cement-type-label-setting" name="cement-type-label-setting" value="السمنت:">
                                </div>
                                <div class="form-group">
                                    <label for="price-per-meter-label-setting"><i class="fas fa-dollar-sign"></i> تسمية حقل سعر المتر:</label>
                                    <input type="text" id="price-per-meter-label-setting" name="price-per-meter-label-setting" value="سعر المتر:">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="pump-wage-label-setting"><i class="fas fa-truck"></i> تسمية حقل أجرة البم:</label>
                                    <input type="text" id="pump-wage-label-setting" name="pump-wage-label-setting" value="أجرة البم:">
                                </div>
                                <div class="form-group">
                                    <label for="total-amount-label-setting"><i class="fas fa-calculator"></i> تسمية حقل المبلغ:</label>
                                    <input type="text" id="total-amount-label-setting" name="total-amount-label-setting" value="المبلغ:">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="received-amount-label-setting"><i class="fas fa-hand-holding-usd"></i> تسمية حقل المبلغ الواصل:</label>
                                    <input type="text" id="received-amount-label-setting" name="received-amount-label-setting" value="المبلغ الواصل:">
                                </div>
                                <div class="form-group">
                                    <label for="beneficiary-name-label-setting"><i class="fas fa-user"></i> تسمية حقل اسم المستفيد:</label>
                                    <input type="text" id="beneficiary-name-label-setting" name="beneficiary-name-label-setting" value="اسم المستفيد:">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="details-label-setting"><i class="fas fa-file-alt"></i> تسمية حقل التفاصيل:</label>
                                    <input type="text" id="details-label-setting" name="details-label-setting" value="التفاصيل:">
                                </div>
                                <div class="form-group">
                                    <label for="notes-label-setting"><i class="fas fa-sticky-note"></i> تسمية حقل الملاحظات:</label>
                                    <input type="text" id="notes-label-setting" name="notes-label-setting" value="الملاحظات:">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Button Labels Settings -->
                    <div class="settings-section">
                        <div class="section-header">
                            <h3><i class="fas fa-mouse-pointer"></i> تسميات الأزرار</h3>
                            <p>تخصيص نصوص وتسميات الأزرار في النظام</p>
                        </div>

                        <div class="form-container">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="add-button-text-setting"><i class="fas fa-plus-circle"></i> نص زر الإضافة:</label>
                                    <input type="text" id="add-button-text-setting" name="add-button-text-setting" value="إضافة">
                                </div>
                                <div class="form-group">
                                    <label for="clear-button-text-setting"><i class="fas fa-eraser"></i> نص زر المسح:</label>
                                    <input type="text" id="clear-button-text-setting" name="clear-button-text-setting" value="مسح">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="preview-button-text-setting"><i class="fas fa-eye"></i> نص زر المعاينة:</label>
                                    <input type="text" id="preview-button-text-setting" name="preview-button-text-setting" value="معاينة قبل الإضافة">
                                </div>
                                <div class="form-group">
                                    <label for="whatsapp-button-text-setting"><i class="fab fa-whatsapp"></i> نص زر واتساب:</label>
                                    <input type="text" id="whatsapp-button-text-setting" name="whatsapp-button-text-setting" value="إرسال على واتساب">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="print-button-text-setting"><i class="fas fa-print"></i> نص زر الطباعة:</label>
                                    <input type="text" id="print-button-text-setting" name="print-button-text-setting" value="طباعة الفاتورة">
                                </div>
                                <div class="form-group">
                                    <label for="edit-button-text-setting"><i class="fas fa-edit"></i> نص زر التعديل:</label>
                                    <input type="text" id="edit-button-text-setting" name="edit-button-text-setting" value="تعديل">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="delete-button-text-setting"><i class="fas fa-trash"></i> نص زر الحذف:</label>
                                    <input type="text" id="delete-button-text-setting" name="delete-button-text-setting" value="حذف">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table Headers Settings -->
                    <div class="settings-section">
                        <div class="section-header">
                            <h3><i class="fas fa-table"></i> عناوين الأعمدة - موقف العمل</h3>
                            <p>تخصيص عناوين أعمدة الجداول</p>
                        </div>

                        <div class="form-container">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="date-header-setting"><i class="fas fa-calendar-alt"></i> عنوان عمود التاريخ:</label>
                                    <input type="text" id="date-header-setting" name="date-header-setting" value="التاريخ">
                                </div>
                                <div class="form-group">
                                    <label for="quantity-header-setting"><i class="fas fa-cubes"></i> عنوان عمود الكمية:</label>
                                    <input type="text" id="quantity-header-setting" name="quantity-header-setting" value="الكمية">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="cement-header-setting"><i class="fas fa-industry"></i> عنوان عمود السمنت:</label>
                                    <input type="text" id="cement-header-setting" name="cement-header-setting" value="السمنت">
                                </div>
                                <div class="form-group">
                                    <label for="price-header-setting"><i class="fas fa-dollar-sign"></i> عنوان عمود سعر المتر:</label>
                                    <input type="text" id="price-header-setting" name="price-header-setting" value="سعر المتر">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="pump-wage-header-setting"><i class="fas fa-truck"></i> عنوان عمود أجرة البم:</label>
                                    <input type="text" id="pump-wage-header-setting" name="pump-wage-header-setting" value="أجرة البم">
                                </div>
                                <div class="form-group">
                                    <label for="total-amount-header-setting"><i class="fas fa-calculator"></i> عنوان عمود المبلغ:</label>
                                    <input type="text" id="total-amount-header-setting" name="total-amount-header-setting" value="المبلغ">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="received-amount-header-setting"><i class="fas fa-hand-holding-usd"></i> عنوان عمود المبلغ الواصل:</label>
                                    <input type="text" id="received-amount-header-setting" name="received-amount-header-setting" value="المبلغ الواصل">
                                </div>
                                <div class="form-group">
                                    <label for="remaining-amount-header-setting"><i class="fas fa-minus-circle"></i> عنوان عمود المبلغ المتبقي:</label>
                                    <input type="text" id="remaining-amount-header-setting" name="remaining-amount-header-setting" value="المبلغ المتبقي">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="beneficiary-header-setting"><i class="fas fa-user"></i> عنوان عمود اسم المستفيد:</label>
                                    <input type="text" id="beneficiary-header-setting" name="beneficiary-header-setting" value="اسم المستفيد">
                                </div>
                                <div class="form-group">
                                    <label for="details-header-setting"><i class="fas fa-file-alt"></i> عنوان عمود التفاصيل:</label>
                                    <input type="text" id="details-header-setting" name="details-header-setting" value="التفاصيل">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="actions-header-setting"><i class="fas fa-cogs"></i> عنوان عمود الإجراءات:</label>
                                    <input type="text" id="actions-header-setting" name="actions-header-setting" value="الإجراءات">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expenses Settings -->
                    <div class="settings-section">
                        <div class="section-header">
                            <h3><i class="fas fa-money-bill-wave"></i> إعدادات المصاريف</h3>
                            <p>تخصيص تسميات حقول المصاريف</p>
                        </div>

                        <div class="form-container">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="expense-date-label-setting"><i class="fas fa-calendar-alt"></i> تسمية حقل تاريخ المصروف:</label>
                                    <input type="text" id="expense-date-label-setting" name="expense-date-label-setting" value="التاريخ:">
                                </div>
                                <div class="form-group">
                                    <label for="expense-amount-label-setting"><i class="fas fa-coins"></i> تسمية حقل مبلغ المصروف:</label>
                                    <input type="text" id="expense-amount-label-setting" name="expense-amount-label-setting" value="المبلغ (بالدينار العراقي):">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="expense-details-label-setting"><i class="fas fa-file-alt"></i> تسمية حقل تفاصيل المصروف:</label>
                                    <input type="text" id="expense-details-label-setting" name="expense-details-label-setting" value="التفاصيل:">
                                </div>
                                <div class="form-group">
                                    <label for="expense-source-label-setting"><i class="fas fa-building"></i> تسمية حقل جهة الصرف:</label>
                                    <input type="text" id="expense-source-label-setting" name="expense-source-label-setting" value="جهة الصرف:">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="expense-status-label-setting"><i class="fas fa-check-circle"></i> تسمية حقل حالة المصروف:</label>
                                    <input type="text" id="expense-status-label-setting" name="expense-status-label-setting" value="الحالة:">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="settings-actions">
                        <div class="buttons-container">
                            <button id="apply-settings-button" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ وتطبيق الإعدادات
                            </button>
                            <button id="reset-settings-button" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> إعادة للوضع الافتراضي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script type="module">
        import * as XLSX from 'https://cdn.sheetjs.com/xlsx-0.20.1/package/xlsx.mjs';

        document.addEventListener('DOMContentLoaded', () => {
            const header = document.querySelector('header');
            let lastScrollTop = 0;

            window.addEventListener('scroll', () => {
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                // Only hide if scrolled past the header
                if (scrollTop > lastScrollTop && scrollTop > header.offsetHeight) {
                    // Scrolling Down
                    header.style.transform = 'translateY(-100%)';
                } else {
                    // Scrolling Up
                    header.style.transform = 'translateY(0)';
                }
                lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
            });

            const workStatusLink = document.getElementById('work-status-link');
            const expensesLink = document.getElementById('expenses-link');
            const dashboardLink = document.getElementById('dashboard-link');

            const workStatusPage = document.getElementById('work-status');
            const expensesPage = document.getElementById('expenses');
            const dashboardPage = document.getElementById('dashboard');

            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const loginButton = document.getElementById('login-button');
            const logoutButton = document.getElementById('logout-button');
            const loginMessage = document.getElementById('login-message');
            const dashboardContent = document.getElementById('dashboard-content');
            const loginContainer = document.querySelector('.login-container');

            // Settings Elements
            const primaryColorSettingInput = document.getElementById('primary-color-setting');
            const secondaryColorSettingInput = document.getElementById('secondary-color-setting');
            const fontFamilySettingSelect = document.getElementById('font-family-setting');
            const siteTitleSettingInput = document.getElementById('site-title-setting');
            const applySettingsButton = document.getElementById('apply-settings-button');
            const resetSettingsButton = document.getElementById('reset-settings-button');

            const pages = [workStatusPage, expensesPage, dashboardPage];
            const dashboardSubPages = [document.getElementById('dashboard-credentials'), document.getElementById('dashboard-contacts'), document.getElementById('dashboard-work-status-view'), document.getElementById('dashboard-expenses-view'), document.getElementById('dashboard-settings')];

            const defaultSettings = {
                primaryColor: '#667eea',
                secondaryColor: '#764ba2',
                headerBgColor: '#667eea',
                buttonBgColor: '#667eea',
                successColor: '#48bb78',
                dangerColor: '#f56565',
                fontFamily: "'Tajawal', sans-serif",
                fontSize: '16px',
                headerFontSize: '2.5rem',
                siteTitle: 'مدير المشاريع',
                workStatusTitle: 'موقف العمل',
                expensesTitle: 'المصاريف',
                dashboardTitle: 'المدخل السري',
                // Field Labels
                dateLabel: 'التاريخ:',
                quantityLabel: 'الكمية:',
                cementTypeLabel: 'السمنت:',
                pricePerMeterLabel: 'سعر المتر:',
                pumpWageLabel: 'أجرة البم:',
                totalAmountLabel: 'المبلغ:',
                receivedAmountLabel: 'المبلغ الواصل:',
                beneficiaryNameLabel: 'اسم المستفيد:',
                detailsLabel: 'التفاصيل:',
                notesLabel: 'الملاحظات:',
                // Button Labels
                addButtonText: 'إضافة',
                clearButtonText: 'مسح',
                previewButtonText: 'معاينة قبل الإضافة',
                whatsappButtonText: 'إرسال على واتساب',
                printButtonText: 'طباعة الفاتورة',
                editButtonText: 'تعديل',
                deleteButtonText: 'حذف',
                // Table Headers
                dateHeader: 'التاريخ',
                quantityHeader: 'الكمية',
                cementHeader: 'السمنت',
                priceHeader: 'سعر المتر',
                pumpWageHeader: 'أجرة البم',
                totalAmountHeader: 'المبلغ',
                receivedAmountHeader: 'المبلغ الواصل',
                remainingAmountHeader: 'المبلغ المتبقي',
                beneficiaryHeader: 'اسم المستفيد',
                detailsHeader: 'التفاصيل',
                actionsHeader: 'الإجراءات',
                // Expenses Labels
                expenseDateLabel: 'التاريخ:',
                expenseAmountLabel: 'المبلغ (بالدينار العراقي):',
                expenseDetailsLabel: 'التفاصيل:',
                expenseSourceLabel: 'جهة الصرف:',
                expenseStatusLabel: 'الحالة:'
            };

            let appSettings = JSON.parse(localStorage.getItem('appSettings')) || { ...defaultSettings };

            let userCredentials = JSON.parse(localStorage.getItem('userCredentials')) || [
                { username: '12', password: '12' }
            ];

            // This will hold the currently filtered data for printing/exporting
            let currentFilteredWorkStatus = [];

            function applySettings() {
                document.documentElement.style.setProperty('--primary-color', appSettings.primaryColor);
                document.documentElement.style.setProperty('--secondary-color', appSettings.secondaryColor);
                document.documentElement.style.setProperty('--success-color', appSettings.successColor);
                document.documentElement.style.setProperty('--danger-color', appSettings.dangerColor);

                // Apply header background
                const header = document.querySelector('header');
                if (header) {
                    header.style.background = `linear-gradient(135deg, ${appSettings.headerBgColor}, ${appSettings.secondaryColor})`;
                }

                // Apply font settings
                document.body.style.fontFamily = appSettings.fontFamily;
                document.body.style.fontSize = appSettings.fontSize;

                // Apply header font size
                const headerTitles = document.querySelectorAll('h2');
                headerTitles.forEach(h => h.style.fontSize = appSettings.headerFontSize);

                document.title = appSettings.siteTitle;
                const headerTitle = document.querySelector('header h1');
                if (headerTitle) headerTitle.textContent = appSettings.siteTitle;

                // Update navigation links
                const workStatusLink = document.getElementById('work-status-link');
                const expensesLink = document.getElementById('expenses-link');
                const dashboardLink = document.getElementById('dashboard-link');

                if (workStatusLink) workStatusLink.textContent = appSettings.workStatusTitle;
                if (expensesLink) expensesLink.textContent = appSettings.expensesTitle;
                if (dashboardLink) dashboardLink.textContent = appSettings.dashboardTitle;

                // Update page titles
                const workStatusPageTitle = document.querySelector('#work-status h2');
                const expensesPageTitle = document.querySelector('#expenses h2');
                const dashboardPageTitle = document.querySelector('#dashboard h2');

                if (workStatusPageTitle) workStatusPageTitle.textContent = appSettings.workStatusTitle;
                if (expensesPageTitle) expensesPageTitle.textContent = appSettings.expensesTitle;
                if (dashboardPageTitle) dashboardPageTitle.textContent = appSettings.dashboardTitle;

                // Update field labels
                updateFieldLabels();
                updateButtonTexts();
                updateTableHeaders();

                // Update settings form fields
                updateSettingsFormFields();
            }

            function updateFieldLabels() {
                // Work Status Page Labels
                const dateLabel = document.querySelector('label[for="date"]');
                const quantityLabel = document.querySelector('label[for="quantity"]');
                const cementTypeLabel = document.querySelector('label[for="cement-type-text"]');
                const pricePerMeterLabel = document.querySelector('label[for="price-per-meter"]');
                const pumpWageLabel = document.querySelector('label[for="pump-wage"]');
                const totalAmountLabel = document.querySelector('label[for="total-amount"]');
                const receivedAmountLabel = document.querySelector('label[for="received-amount"]');
                const beneficiaryNameLabel = document.querySelector('label[for="beneficiary-name"]');
                const detailsLabel = document.querySelector('label[for="details"]');
                const notesLabel = document.querySelector('label[for="notes"]');

                if (dateLabel) dateLabel.textContent = appSettings.dateLabel;
                if (quantityLabel) quantityLabel.textContent = appSettings.quantityLabel;
                if (cementTypeLabel) cementTypeLabel.textContent = appSettings.cementTypeLabel;
                if (pricePerMeterLabel) pricePerMeterLabel.textContent = appSettings.pricePerMeterLabel;
                if (pumpWageLabel) pumpWageLabel.textContent = appSettings.pumpWageLabel;
                if (totalAmountLabel) totalAmountLabel.textContent = appSettings.totalAmountLabel;
                if (receivedAmountLabel) receivedAmountLabel.textContent = appSettings.receivedAmountLabel;
                if (beneficiaryNameLabel) beneficiaryNameLabel.textContent = appSettings.beneficiaryNameLabel;
                if (detailsLabel) detailsLabel.textContent = appSettings.detailsLabel;
                if (notesLabel) notesLabel.textContent = appSettings.notesLabel;

                // Expenses Page Labels
                const expenseDateLabel = document.querySelector('label[for="expense-date"]');
                const expenseAmountLabel = document.querySelector('label[for="expense-amount"]');
                const expenseDetailsLabel = document.querySelector('label[for="expense-details"]');
                const expenseSourceLabel = document.querySelector('label[for="expense-source"]');
                const expenseStatusLabel = document.querySelector('label[for="expense-status"]');

                if (expenseDateLabel) expenseDateLabel.textContent = appSettings.expenseDateLabel;
                if (expenseAmountLabel) expenseAmountLabel.textContent = appSettings.expenseAmountLabel;
                if (expenseDetailsLabel) expenseDetailsLabel.textContent = appSettings.expenseDetailsLabel;
                if (expenseSourceLabel) expenseSourceLabel.textContent = appSettings.expenseSourceLabel;
                if (expenseStatusLabel) expenseStatusLabel.textContent = appSettings.expenseStatusLabel;
            }

            function updateButtonTexts() {
                const addDataButton = document.getElementById('add-data-button');
                const clearButton = document.getElementById('clear-button');
                const previewButton = document.getElementById('preview-button');
                const whatsappButton = document.getElementById('whatsapp-button');
                const printInvoiceButton = document.getElementById('print-invoice-button');
                const addExpenseButton = document.getElementById('add-expense-button');
                const clearExpenseButton = document.getElementById('clear-expense-button');

                if (addDataButton) addDataButton.innerHTML = `<i class="fas fa-plus-circle"></i> ${appSettings.addButtonText}`;
                if (clearButton) clearButton.innerHTML = `<i class="fas fa-eraser"></i> ${appSettings.clearButtonText}`;
                if (previewButton) previewButton.innerHTML = `<i class="fas fa-search"></i> ${appSettings.previewButtonText}`;
                if (whatsappButton) whatsappButton.innerHTML = `<i class="fab fa-whatsapp"></i> ${appSettings.whatsappButtonText}`;
                if (printInvoiceButton) printInvoiceButton.innerHTML = `<i class="fas fa-print"></i> ${appSettings.printButtonText}`;
                if (addExpenseButton) addExpenseButton.innerHTML = `<i class="fas fa-plus-circle"></i> ${appSettings.addButtonText} مصروف`;
                if (clearExpenseButton) clearExpenseButton.innerHTML = `<i class="fas fa-eraser"></i> ${appSettings.clearButtonText}`;
            }

            function updateTableHeaders() {
                // Work Status Table Headers - الإجراءات في العمود الأول
                const dashboardWorkStatusTable = document.querySelector('#dashboard-work-status-table thead tr');
                if (dashboardWorkStatusTable) {
                    dashboardWorkStatusTable.innerHTML = `
                        <th style="background: linear-gradient(135deg, #48bb78, #38a169); color: white;">${appSettings.actionsHeader}</th>
                        <th>${appSettings.dateHeader}</th>
                        <th>${appSettings.quantityHeader}</th>
                        <th>${appSettings.cementHeader}</th>
                        <th>${appSettings.priceHeader}</th>
                        <th>${appSettings.pumpWageHeader}</th>
                        <th>${appSettings.totalAmountHeader}</th>
                        <th>${appSettings.receivedAmountHeader}</th>
                        <th>${appSettings.remainingAmountHeader}</th>
                        <th>نوع السمنت</th>
                        <th>المقاومة</th>
                        <th>${appSettings.beneficiaryHeader}</th>
                        <th>${appSettings.detailsHeader}</th>
                        <th>المضاف</th>
                        <th>اسم الموظف</th>
                        <th>تاريخ التسديد</th>
                        <th>رقم الهاتف</th>
                        <th>الملاحظات</th>
                    `;
                }

                // Expenses Table Headers
                const expensesTable = document.querySelector('#expenses-table thead tr');
                const dashboardExpensesTable = document.querySelector('#dashboard-expenses-table thead tr');

                const expenseHeaders = `
                    <th>${appSettings.dateHeader}</th>
                    <th>المبلغ (د.ع)</th>
                    <th>${appSettings.detailsHeader}</th>
                    <th>جهة الصرف</th>
                    <th>الحالة</th>
                    <th>${appSettings.actionsHeader}</th>
                `;

                if (expensesTable) expensesTable.innerHTML = expenseHeaders;
                if (dashboardExpensesTable) dashboardExpensesTable.innerHTML = expenseHeaders;
            }

            function updateSettingsFormFields() {
                document.getElementById('primary-color-setting').value = appSettings.primaryColor;
                document.getElementById('secondary-color-setting').value = appSettings.secondaryColor;
                document.getElementById('header-bg-color-setting').value = appSettings.headerBgColor;
                document.getElementById('button-bg-color-setting').value = appSettings.buttonBgColor;
                document.getElementById('success-color-setting').value = appSettings.successColor;
                document.getElementById('danger-color-setting').value = appSettings.dangerColor;

                // Font Settings
                document.getElementById('font-family-setting').value = appSettings.fontFamily;
                document.getElementById('font-size-setting').value = appSettings.fontSize;
                document.getElementById('header-font-size-setting').value = appSettings.headerFontSize;

                // General Settings
                document.getElementById('site-title-setting').value = appSettings.siteTitle;
                document.getElementById('work-status-title-setting').value = appSettings.workStatusTitle;
                document.getElementById('expenses-title-setting').value = appSettings.expensesTitle;
                document.getElementById('dashboard-title-setting').value = appSettings.dashboardTitle;

                // Field Labels
                document.getElementById('date-label-setting').value = appSettings.dateLabel;
                document.getElementById('quantity-label-setting').value = appSettings.quantityLabel;
                document.getElementById('cement-type-label-setting').value = appSettings.cementTypeLabel;
                document.getElementById('price-per-meter-label-setting').value = appSettings.pricePerMeterLabel;
                document.getElementById('pump-wage-label-setting').value = appSettings.pumpWageLabel;
                document.getElementById('total-amount-label-setting').value = appSettings.totalAmountLabel;
                document.getElementById('received-amount-label-setting').value = appSettings.receivedAmountLabel;
                document.getElementById('beneficiary-name-label-setting').value = appSettings.beneficiaryNameLabel;
                document.getElementById('details-label-setting').value = appSettings.detailsLabel;
                document.getElementById('notes-label-setting').value = appSettings.notesLabel;

                // Button Labels
                document.getElementById('add-button-text-setting').value = appSettings.addButtonText;
                document.getElementById('clear-button-text-setting').value = appSettings.clearButtonText;
                document.getElementById('preview-button-text-setting').value = appSettings.previewButtonText;
                document.getElementById('whatsapp-button-text-setting').value = appSettings.whatsappButtonText;
                document.getElementById('print-button-text-setting').value = appSettings.printButtonText;
                document.getElementById('edit-button-text-setting').value = appSettings.editButtonText;
                document.getElementById('delete-button-text-setting').value = appSettings.deleteButtonText;

                // Table Headers
                document.getElementById('date-header-setting').value = appSettings.dateHeader;
                document.getElementById('quantity-header-setting').value = appSettings.quantityHeader;
                document.getElementById('cement-header-setting').value = appSettings.cementHeader;
                document.getElementById('price-header-setting').value = appSettings.priceHeader;
                document.getElementById('pump-wage-header-setting').value = appSettings.pumpWageHeader;
                document.getElementById('total-amount-header-setting').value = appSettings.totalAmountHeader;
                document.getElementById('received-amount-header-setting').value = appSettings.receivedAmountHeader;
                document.getElementById('remaining-amount-header-setting').value = appSettings.remainingAmountHeader;
                document.getElementById('beneficiary-header-setting').value = appSettings.beneficiaryHeader;
                document.getElementById('details-header-setting').value = appSettings.detailsHeader;
                document.getElementById('actions-header-setting').value = appSettings.actionsHeader;

                // Expenses Labels
                document.getElementById('expense-date-label-setting').value = appSettings.expenseDateLabel;
                document.getElementById('expense-amount-label-setting').value = appSettings.expenseAmountLabel;
                document.getElementById('expense-details-label-setting').value = appSettings.expenseDetailsLabel;
                document.getElementById('expense-source-label-setting').value = appSettings.expenseSourceLabel;
                document.getElementById('expense-status-label-setting').value = appSettings.expenseStatusLabel;
            }

            function saveAndApplySettings() {
                 appSettings = {
                    primaryColor: document.getElementById('primary-color-setting').value,
                    secondaryColor: document.getElementById('secondary-color-setting').value,
                    headerBgColor: document.getElementById('header-bg-color-setting').value,
                    buttonBgColor: document.getElementById('button-bg-color-setting').value,
                    successColor: document.getElementById('success-color-setting').value,
                    dangerColor: document.getElementById('danger-color-setting').value,
                    fontFamily: document.getElementById('font-family-setting').value,
                    fontSize: document.getElementById('font-size-setting').value,
                    headerFontSize: document.getElementById('header-font-size-setting').value,
                    siteTitle: document.getElementById('site-title-setting').value,
                    workStatusTitle: document.getElementById('work-status-title-setting').value,
                    expensesTitle: document.getElementById('expenses-title-setting').value,
                    dashboardTitle: document.getElementById('dashboard-title-setting').value,
                    dateLabel: document.getElementById('date-label-setting').value,
                    quantityLabel: document.getElementById('quantity-label-setting').value,
                    cementTypeLabel: document.getElementById('cement-type-label-setting').value,
                    pricePerMeterLabel: document.getElementById('price-per-meter-label-setting').value,
                    pumpWageLabel: document.getElementById('pump-wage-label-setting').value,
                    totalAmountLabel: document.getElementById('total-amount-label-setting').value,
                    receivedAmountLabel: document.getElementById('received-amount-label-setting').value,
                    beneficiaryNameLabel: document.getElementById('beneficiary-name-label-setting').value,
                    detailsLabel: document.getElementById('details-label-setting').value,
                    notesLabel: document.getElementById('notes-label-setting').value,
                    addButtonText: document.getElementById('add-button-text-setting').value,
                    clearButtonText: document.getElementById('clear-button-text-setting').value,
                    previewButtonText: document.getElementById('preview-button-text-setting').value,
                    whatsappButtonText: document.getElementById('whatsapp-button-text-setting').value,
                    printButtonText: document.getElementById('print-button-text-setting').value,
                    editButtonText: document.getElementById('edit-button-text-setting').value,
                    deleteButtonText: document.getElementById('delete-button-text-setting').value,
                    dateHeader: document.getElementById('date-header-setting').value,
                    quantityHeader: document.getElementById('quantity-header-setting').value,
                    cementHeader: document.getElementById('cement-header-setting').value,
                    priceHeader: document.getElementById('price-header-setting').value,
                    pumpWageHeader: document.getElementById('pump-wage-header-setting').value,
                    totalAmountHeader: document.getElementById('total-amount-header-setting').value,
                    receivedAmountHeader: document.getElementById('received-amount-header-setting').value,
                    remainingAmountHeader: document.getElementById('remaining-amount-header-setting').value,
                    beneficiaryHeader: document.getElementById('beneficiary-header-setting').value,
                    detailsHeader: document.getElementById('details-header-setting').value,
                    actionsHeader: document.getElementById('actions-header-setting').value,
                    expenseDateLabel: document.getElementById('expense-date-label-setting').value,
                    expenseAmountLabel: document.getElementById('expense-amount-label-setting').value,
                    expenseDetailsLabel: document.getElementById('expense-details-label-setting').value,
                    expenseSourceLabel: document.getElementById('expense-source-label-setting').value,
                    expenseStatusLabel: document.getElementById('expense-status-label-setting').value
                 };

                localStorage.setItem('appSettings', JSON.stringify(appSettings));
                applySettings();
                alert('تم حفظ وتطبيق الإعدادات بنجاح!');
            }

            function resetAndApplySettings() {
                 appSettings = { ...defaultSettings };
                 localStorage.setItem('appSettings', JSON.stringify(appSettings));
                 applySettings();
                 alert('تمت إعادة الإعدادات إلى الوضع الافتراضي.');
            }

            applySettings();

            function showPage(pageToShow) {
                pages.forEach(page => {
                    page.classList.remove('active');
                });
                pageToShow.classList.add('active');
            }

            function showDashboardSubPage(subPageToShow) {
                dashboardSubPages.forEach(subPage => {
                    subPage.classList.remove('active');
                });
                document.getElementById('dashboard-portals-view').style.display = 'none';
                subPageToShow.classList.add('active');
            }

            function showDashboardPortals() {
                dashboardSubPages.forEach(subPage => {
                    subPage.classList.remove('active');
                });
                document.getElementById('dashboard-portals-view').style.display = 'block';
            }

            workStatusLink.addEventListener('click', (e) => {
                e.preventDefault();
                showPage(workStatusPage);
            });

            expensesLink.addEventListener('click', (e) => {
                e.preventDefault();
                showPage(expensesPage);
            });

            dashboardLink.addEventListener('click', (e) => {
                e.preventDefault();
                showPage(dashboardPage);

                loginContainer.style.display = 'flex';
                dashboardContent.style.display = 'none';
                loginMessage.textContent = '';
                usernameInput.value = '';
                passwordInput.value = '';
            });

            loginButton.addEventListener('click', () => {
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();

                const userExists = userCredentials.some(cred => cred.username === username && cred.password === password);

                if (userExists) {
                    loginMessage.textContent = '';
                    loginContainer.style.display = 'none';
                    dashboardContent.style.display = 'block';
                    showDashboardPortals();
                } else {
                    loginMessage.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
                }
            });

            logoutButton.addEventListener('click', () => {
                loginContainer.style.display = 'flex';
                dashboardContent.style.display = 'none';
                loginMessage.textContent = 'تم تسجيل الخروج بنجاح.';
                usernameInput.value = '';
                passwordInput.value = '';
                // عند تسجيل الخروج، العودة إلى صفحة العمل الرئيسية
                showPage(workStatusPage);
            });

            const portalCards = document.querySelectorAll('.portal-card');
            const backToPortalsButtons = document.querySelectorAll('.back-to-portals');

            portalCards.forEach(card => {
                card.addEventListener('click', (e) => {
                    const portal = e.currentTarget.dataset.portal;
                    if (portal === 'credentials') {
                        showDashboardSubPage(document.getElementById('dashboard-credentials'));
                    } else if (portal === 'contacts') {
                        showDashboardSubPage(document.getElementById('dashboard-contacts'));
                    } else if (portal === 'employees') {
                        showDashboardSubPage(document.getElementById('dashboard-employees'));
                        displayEmployeesData();
                    } else if (portal === 'work-status-dashboard') {
                        displayDashboardWorkStatusData(JSON.parse(localStorage.getItem('workStatusData')) || []);
                        showDashboardSubPage(document.getElementById('dashboard-work-status-view'));
                        // تفعيل الاقتراح التلقائي لحقل البحث بالاسم
                        setTimeout(() => {
                            setupSearchAutocomplete('search-beneficiary', 'search-beneficiary-suggestions', getWorkStatusBeneficiaryNames);
                        }, 100);
                    } else if (portal === 'expenses-dashboard') {
                        displayDashboardExpensesData(JSON.parse(localStorage.getItem('expensesData')) || []);
                        showDashboardSubPage(document.getElementById('dashboard-expenses-view'));
                        // تفعيل الاقتراح التلقائي لحقل البحث بجهة الصرف
                        setTimeout(() => {
                            setupSearchAutocomplete('search-expense-source', 'search-expense-source-suggestions', getExpenseSources);
                        }, 100);
                    } else if (portal === 'settings') {
                        showDashboardSubPage(document.getElementById('dashboard-settings'));
                        primaryColorSettingInput.value = appSettings.primaryColor;
                        secondaryColorSettingInput.value = appSettings.secondaryColor;
                        fontFamilySettingSelect.value = appSettings.fontFamily;
                        siteTitleSettingInput.value = appSettings.siteTitle;
                    }
                });
            });

            backToPortalsButtons.forEach(button => {
                button.addEventListener('click', showDashboardPortals);
            });

            // Settings Event Listeners
            applySettingsButton.addEventListener('click', saveAndApplySettings);
            resetSettingsButton.addEventListener('click', resetAndApplySettings);

            // Work Status - Main Page
            const dateInput = document.getElementById('date');
            const quantityInput = document.getElementById('quantity');
            const cementTypeText = document.getElementById('cement-type-text');
            const pricePerMeterInput = document.getElementById('price-per-meter');
            const pumpWageInput = document.getElementById('pump-wage');
            const totalAmountInput = document.getElementById('total-amount');
            const receivedAmountInput = document.getElementById('received-amount');
            const cementTypeDropdown = document.getElementById('cement-type-dropdown');
            const resistanceDropdown = document.getElementById('resistance');
            const beneficiaryNameInput = document.getElementById('beneficiary-name');
            const detailsInput = document.getElementById('details');
            const addedByInput = document.getElementById('added-by');
            const paymentDateInput = document.getElementById('payment-date');
            const phoneNumberDropdown = document.getElementById('phone-number');
            const newPhoneNumberInput = document.getElementById('new-phone-number');
            const notesInput = document.getElementById('notes');

            const previewButton = document.getElementById('preview-button');
            const addDataButton = document.getElementById('add-data-button');
            const clearButton = document.getElementById('clear-button');
            const whatsappButton = document.getElementById('whatsapp-button');
            const printInvoiceButton = document.getElementById('print-invoice-button');

            const invoicePrintArea = document.getElementById('invoice-print-area');

            let savedContacts = JSON.parse(localStorage.getItem('savedContacts')) || [];
            let savedEmployees = JSON.parse(localStorage.getItem('savedEmployees')) || [];
            let workStatusData = JSON.parse(localStorage.getItem('workStatusData')) || [];

            // متغيرات للاقتراح التلقائي
            let currentSuggestionIndex = -1;
            let filteredSuggestions = [];

            function calculateTotal() {
                const quantity = parseFloat(quantityInput.value) || 0;
                const pricePerMeter = parseFloat(pricePerMeterInput.value) || 0;
                const pumpWage = parseFloat(pumpWageInput.value) || 0;
                const total = (quantity * pricePerMeter) + pumpWage;
                totalAmountInput.value = total.toFixed(2);
            }

            // دوال الاقتراح التلقائي لأسماء المستفيدين
            function getBeneficiaryNames() {
                const names = new Set();
                workStatusData.forEach(item => {
                    if (item.beneficiaryName && item.beneficiaryName.trim()) {
                        names.add(item.beneficiaryName.trim());
                    }
                });
                return Array.from(names).sort();
            }

            function showSuggestions(input, suggestions) {
                const suggestionsList = document.getElementById('beneficiary-suggestions');
                suggestionsList.innerHTML = '';

                if (suggestions.length === 0) {
                    suggestionsList.classList.remove('show');
                    return;
                }

                suggestions.forEach((suggestion, index) => {
                    const suggestionItem = document.createElement('div');
                    suggestionItem.className = 'suggestion-item';
                    suggestionItem.textContent = suggestion;
                    suggestionItem.addEventListener('click', () => {
                        input.value = suggestion;
                        suggestionsList.classList.remove('show');
                        currentSuggestionIndex = -1;
                        input.focus();
                    });
                    suggestionsList.appendChild(suggestionItem);
                });

                suggestionsList.classList.add('show');
                currentSuggestionIndex = -1;
            }

            function hideSuggestions() {
                const suggestionsList = document.getElementById('beneficiary-suggestions');
                setTimeout(() => {
                    suggestionsList.classList.remove('show');
                }, 150);
            }

            function highlightSuggestion(index) {
                const suggestionItems = document.querySelectorAll('.suggestion-item');
                suggestionItems.forEach((item, i) => {
                    if (i === index) {
                        item.classList.add('highlighted');
                        item.scrollIntoView({ block: 'nearest' });
                    } else {
                        item.classList.remove('highlighted');
                    }
                });
            }

            function selectHighlightedSuggestion() {
                const highlightedItem = document.querySelector('.suggestion-item.highlighted');
                if (highlightedItem) {
                    beneficiaryNameInput.value = highlightedItem.textContent;
                    hideSuggestions();
                    currentSuggestionIndex = -1;
                }
            }

            function populateEmployeeNames() {
                const employeeDropdown = document.getElementById('employee-name');
                employeeDropdown.innerHTML = '<option value="">اختر اسم الموظف</option>';

                savedEmployees.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.name;
                    option.textContent = employee.name;
                    option.dataset.phone = employee.phone;
                    employeeDropdown.appendChild(option);
                });
            }

            quantityInput.addEventListener('input', calculateTotal);
            pricePerMeterInput.addEventListener('input', calculateTotal);
            pumpWageInput.addEventListener('input', calculateTotal);

            // إضافة event listeners لحقل اسم المستفيد
            beneficiaryNameInput.addEventListener('input', function(e) {
                const inputValue = e.target.value.trim();

                if (inputValue.length === 0) {
                    hideSuggestions();
                    return;
                }

                const allNames = getBeneficiaryNames();
                filteredSuggestions = allNames.filter(name =>
                    name.toLowerCase().includes(inputValue.toLowerCase())
                );

                showSuggestions(e.target, filteredSuggestions);
            });

            beneficiaryNameInput.addEventListener('keydown', function(e) {
                const suggestionItems = document.querySelectorAll('.suggestion-item');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    if (currentSuggestionIndex < suggestionItems.length - 1) {
                        currentSuggestionIndex++;
                        highlightSuggestion(currentSuggestionIndex);
                    }
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    if (currentSuggestionIndex > 0) {
                        currentSuggestionIndex--;
                        highlightSuggestion(currentSuggestionIndex);
                    }
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (currentSuggestionIndex >= 0) {
                        selectHighlightedSuggestion();
                    }
                } else if (e.key === 'Escape') {
                    hideSuggestions();
                    currentSuggestionIndex = -1;
                }
            });

            beneficiaryNameInput.addEventListener('blur', function() {
                hideSuggestions();
            });

            beneficiaryNameInput.addEventListener('focus', function(e) {
                const inputValue = e.target.value.trim();
                if (inputValue.length > 0) {
                    const allNames = getBeneficiaryNames();
                    filteredSuggestions = allNames.filter(name =>
                        name.toLowerCase().includes(inputValue.toLowerCase())
                    );
                    showSuggestions(e.target, filteredSuggestions);
                }
            });

            // إخفاء الاقتراحات عند النقر خارج الحقل
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.autocomplete-container')) {
                    hideSuggestions();
                    currentSuggestionIndex = -1;
                }
            });

            // دوال الاقتراح التلقائي لحقول البحث في Dashboard
            function setupSearchAutocomplete(inputId, suggestionsId, dataSource) {
                const input = document.getElementById(inputId);
                const suggestionsList = document.getElementById(suggestionsId);
                let currentIndex = -1;
                let filteredItems = [];

                if (!input || !suggestionsList) return;

                function showSearchSuggestions(items) {
                    suggestionsList.innerHTML = '';

                    if (items.length === 0) {
                        suggestionsList.classList.remove('show');
                        return;
                    }

                    items.forEach((item, index) => {
                        const suggestionItem = document.createElement('div');
                        suggestionItem.className = 'suggestion-item';
                        suggestionItem.textContent = item;
                        suggestionItem.addEventListener('click', () => {
                            input.value = item;
                            suggestionsList.classList.remove('show');
                            currentIndex = -1;
                            input.focus();
                        });
                        suggestionsList.appendChild(suggestionItem);
                    });

                    suggestionsList.classList.add('show');
                    currentIndex = -1;
                }

                function hideSearchSuggestions() {
                    setTimeout(() => {
                        suggestionsList.classList.remove('show');
                    }, 150);
                }

                function highlightSearchSuggestion(index) {
                    const suggestionItems = suggestionsList.querySelectorAll('.suggestion-item');
                    suggestionItems.forEach((item, i) => {
                        if (i === index) {
                            item.classList.add('highlighted');
                            item.scrollIntoView({ block: 'nearest' });
                        } else {
                            item.classList.remove('highlighted');
                        }
                    });
                }

                input.addEventListener('input', function(e) {
                    const inputValue = e.target.value.trim();

                    if (inputValue.length === 0) {
                        hideSearchSuggestions();
                        return;
                    }

                    const allItems = dataSource();
                    filteredItems = allItems.filter(item =>
                        item.toLowerCase().includes(inputValue.toLowerCase())
                    );

                    showSearchSuggestions(filteredItems);
                });

                input.addEventListener('keydown', function(e) {
                    const suggestionItems = suggestionsList.querySelectorAll('.suggestion-item');

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (currentIndex < suggestionItems.length - 1) {
                            currentIndex++;
                            highlightSearchSuggestion(currentIndex);
                        }
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (currentIndex > 0) {
                            currentIndex--;
                            highlightSearchSuggestion(currentIndex);
                        }
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        if (currentIndex >= 0) {
                            const highlightedItem = suggestionsList.querySelector('.suggestion-item.highlighted');
                            if (highlightedItem) {
                                input.value = highlightedItem.textContent;
                                hideSearchSuggestions();
                                currentIndex = -1;
                            }
                        }
                    } else if (e.key === 'Escape') {
                        hideSearchSuggestions();
                        currentIndex = -1;
                    }
                });

                input.addEventListener('blur', function() {
                    hideSearchSuggestions();
                });

                input.addEventListener('focus', function(e) {
                    const inputValue = e.target.value.trim();
                    if (inputValue.length > 0) {
                        const allItems = dataSource();
                        filteredItems = allItems.filter(item =>
                            item.toLowerCase().includes(inputValue.toLowerCase())
                        );
                        showSearchSuggestions(filteredItems);
                    }
                });
            }

            // دالة للحصول على أسماء المستفيدين من بيانات موقف العمل
            function getWorkStatusBeneficiaryNames() {
                const names = new Set();
                workStatusData.forEach(item => {
                    if (item.beneficiaryName && item.beneficiaryName.trim()) {
                        names.add(item.beneficiaryName.trim());
                    }
                });
                return Array.from(names).sort();
            }

            // دالة للحصول على جهات الصرف من بيانات المصاريف
            function getExpenseSources() {
                const sources = new Set();
                const expensesData = JSON.parse(localStorage.getItem('expensesData')) || [];
                expensesData.forEach(item => {
                    if (item.source && item.source.trim()) {
                        sources.add(item.source.trim());
                    }
                });
                return Array.from(sources).sort();
            }

            // Handle phone number selection
            phoneNumberDropdown.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.dataset.phone) {
                    newPhoneNumberInput.value = selectedOption.dataset.phone;
                    newPhoneNumberInput.style.display = 'block';
                } else {
                    newPhoneNumberInput.value = '';
                }
            });

            function clearWorkStatusForm() {
                 dateInput.value = new Date().toISOString().split('T')[0];
                quantityInput.value = '';
                cementTypeText.value = '';
                pricePerMeterInput.value = '';
                pumpWageInput.value = '';
                totalAmountInput.value = '';
                receivedAmountInput.value = '';
                cementTypeDropdown.value = '';
                resistanceDropdown.value = '';
                beneficiaryNameInput.value = '';
                detailsInput.value = '';
                addedByInput.value = '';
                document.getElementById('employee-name').value = '';
                paymentDateInput.value = '';
                phoneNumberDropdown.value = '';
                newPhoneNumberInput.value = '';
                notesInput.value = '';
            }

            dateInput.value = new Date().toISOString().split('T')[0];

            addDataButton.addEventListener('click', () => {
                const phone = phoneNumberDropdown.value || newPhoneNumberInput.value;
                if (!phone && newPhoneNumberInput.value) {
                    const contactName = beneficiaryNameInput.value || 'غير محدد';
                    if(confirm(`هل تريد حفظ ${newPhoneNumberInput.value} كجهة اتصال جديدة لـ ${contactName}?`)) {
                        const newContact = { name: contactName, phone: newPhoneNumberInput.value };
                        savedContacts.push(newContact);
                        localStorage.setItem('savedContacts', JSON.stringify(savedContacts));
                        populatePhoneNumbers();
                        displayContactsData();
                    }
                }
                const data = {
                    id: Date.now(),
                    date: dateInput.value,
                    quantity: parseFloat(quantityInput.value) || 0,
                    cementTypeText: cementTypeText.value,
                    pricePerMeter: parseFloat(pricePerMeterInput.value) || 0,
                    pumpWage: parseFloat(pumpWageInput.value) || 0,
                    totalAmount: parseFloat(totalAmountInput.value) || 0,
                    receivedAmount: parseFloat(receivedAmountInput.value) || 0,
                    cementTypeDropdown: cementTypeDropdown.value,
                    resistance: resistanceDropdown.value,
                    beneficiaryName: beneficiaryNameInput.value,
                    details: detailsInput.value,
                    addedBy: addedByInput.value,
                    employeeName: document.getElementById('employee-name').value,
                    paymentDate: paymentDateInput.value,
                    phoneNumber: phone,
                    notes: notesInput.value
                };

                workStatusData.push(data);
                localStorage.setItem('workStatusData', JSON.stringify(workStatusData));
                displayDashboardWorkStatusData(workStatusData);
                clearWorkStatusForm();
                alert('تمت إضافة البيانات بنجاح!');

                // تحديث الاقتراحات في حقل البحث إذا كان مفتوحاً
                if (document.getElementById('search-beneficiary')) {
                    setupSearchAutocomplete('search-beneficiary', 'search-beneficiary-suggestions', getWorkStatusBeneficiaryNames);
                }
            });

            clearButton.addEventListener('click', clearWorkStatusForm);

            // Preview Button Event Listener
            previewButton.addEventListener('click', () => {
                showInvoicePreview();
            });

            whatsappButton.addEventListener('click', () => {
                const selectedPhoneNumber = phoneNumberDropdown.value || newPhoneNumberInput.value;
                if (!selectedPhoneNumber) {
                    alert('الرجاء اختيار أو إدخال رقم هاتف لإرسال الرسالة.');
                    return;
                }

                const data = {
                    date: dateInput.value,
                    quantity: quantityInput.value,
                    cementTypeText: cementTypeText.value,
                    pricePerMeter: pricePerMeterInput.value,
                    pumpWage: pumpWageInput.value,
                    totalAmount: totalAmountInput.value,
                    receivedAmount: receivedAmountInput.value,
                    cementTypeDropdown: cementTypeDropdown.value,
                    resistance: resistanceDropdown.value,
                    beneficiaryName: beneficiaryNameInput.value,
                    details: detailsInput.value,
                    addedBy: addedByInput.value,
                    paymentDate: paymentDateInput.value,
                    phoneNumber: selectedPhoneNumber,
                    notes: notesInput.value
                };

                const message = `
                    فاتورة عمل
                    -----------------
                    المستفيد: ${data.beneficiaryName}
                    التاريخ: ${data.date}
                    الكمية: ${data.quantity} م³
                    نوع السمنت: ${data.cementTypeDropdown} (${data.cementTypeText})
                    المقاومة: ${data.resistance}
                    سعر المتر: ${data.pricePerMeter}
                    أجرة البم: ${data.pumpWage}
                    -----------------
                    إجمالي المبلغ: ${data.totalAmount}
                    المبلغ الواصل: ${data.receivedAmount}
                    -----------------
                    التفاصيل: ${data.details}
                    الملاحظات: ${data.notes}
                `;

                const whatsappUrl = `https://wa.me/${selectedPhoneNumber}?text=${encodeURIComponent(message.trim())}`;
                window.open(whatsappUrl, '_blank');
            });

            printInvoiceButton.addEventListener('click', () => {
                printProfessionalInvoice();
            });

            // Enhanced Print Function
            function printEnhancedInvoice() {
                const currentDate = new Date();
                const invoiceNumber = `INV-${currentDate.getFullYear()}${(currentDate.getMonth() + 1).toString().padStart(2, '0')}${currentDate.getDate().toString().padStart(2, '0')}-${Date.now().toString().slice(-4)}`;
                const remainingAmount = (parseFloat(totalAmountInput.value) || 0) - (parseFloat(receivedAmountInput.value) || 0);

                invoicePrintArea.innerHTML = `
                    <style>
                        @media print {
                            body * { visibility: hidden; }
                            .print-invoice, .print-invoice * { visibility: visible; }
                            .print-invoice {
                                position: absolute;
                                left: 0;
                                top: 0;
                                width: 100%;
                                background: white !important;
                                -webkit-print-color-adjust: exact;
                                color-adjust: exact;
                            }
                            @page {
                                size: A4;
                                margin: 15mm;
                            }
                        }
                        .print-invoice {
                            font-family: 'Tajawal', sans-serif;
                            direction: rtl;
                            color: #333;
                            line-height: 1.6;
                            max-width: 800px;
                            margin: 0 auto;
                            background: white;
                            padding: 40px;
                        }
                        .print-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 40px;
                            padding-bottom: 20px;
                            border-bottom: 3px solid #667eea;
                        }
                        .print-logo h1 {
                            color: #667eea;
                            font-size: 2.5rem;
                            font-weight: 900;
                            margin: 0;
                        }
                        .print-logo p {
                            color: #666;
                            margin: 5px 0;
                            font-size: 1rem;
                        }
                        .print-invoice-number {
                            text-align: left;
                            color: #666;
                        }
                        .print-invoice-number h3 {
                            color: #667eea;
                            font-size: 1.5rem;
                            margin: 0;
                        }
                        .print-details {
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 30px;
                            margin-bottom: 30px;
                        }
                        .print-to, .print-from {
                            background: #f8fafc;
                            padding: 20px;
                            border-radius: 10px;
                            border-right: 4px solid #667eea;
                        }
                        .print-to h4, .print-from h4 {
                            color: #667eea;
                            margin-bottom: 15px;
                            font-size: 1.2rem;
                        }
                        .print-table {
                            width: 100%;
                            border-collapse: separate;
                            border-spacing: 0;
                            margin: 30px 0;
                            border-radius: 10px;
                            overflow: hidden;
                            border: 1px solid #e2e8f0;
                        }
                        .print-table th {
                            background: #667eea;
                            color: white;
                            padding: 15px;
                            text-align: center;
                            font-weight: 700;
                            font-size: 1.1rem;
                        }
                        .print-table td {
                            padding: 15px;
                            text-align: center;
                            border-bottom: 1px solid #e2e8f0;
                            background: white;
                        }
                        .print-table tr:nth-child(even) td {
                            background: #f8fafc;
                        }
                        .print-table .total-row td {
                            background: #edf2f7;
                            font-weight: 700;
                            font-size: 1.1rem;
                            color: #667eea;
                        }
                        .print-summary {
                            display: grid;
                            grid-template-columns: 1fr auto;
                            gap: 30px;
                            margin-top: 30px;
                        }
                        .print-notes {
                            background: #f8fafc;
                            padding: 20px;
                            border-radius: 10px;
                            border-right: 4px solid #4299e1;
                        }
                        .print-notes h4 {
                            color: #4299e1;
                            margin-bottom: 10px;
                        }
                        .print-totals {
                            background: white;
                            padding: 20px;
                            border-radius: 10px;
                            border: 2px solid #667eea;
                            min-width: 300px;
                        }
                        .print-totals .total-line {
                            display: flex;
                            justify-content: space-between;
                            padding: 10px 0;
                            border-bottom: 1px dashed #e2e8f0;
                        }
                        .print-totals .total-line:last-child {
                            border-bottom: none;
                            font-weight: 700;
                            font-size: 1.2rem;
                            color: #667eea;
                            background: #f0f8ff;
                            padding: 15px;
                            margin: 10px -20px -20px -20px;
                            border-radius: 0 0 8px 8px;
                        }
                    </style>
                    <div class="print-invoice">
                        <div class="print-header">
                            <div class="print-logo">
                                <h1>فاتورة عمل</h1>
                                <p>شركة المشاريع الإنشائية</p>
                                <p>العراق - بغداد</p>
                            </div>
                            <div class="print-invoice-number">
                                <h3>رقم الفاتورة</h3>
                                <p>${invoiceNumber}</p>
                                <p>التاريخ: ${new Date(dateInput.value || new Date()).toLocaleDateString('ar-IQ')}</p>
                            </div>
                        </div>

                        <div class="print-details">
                            <div class="print-to">
                                <h4>إلى:</h4>
                                <p><strong>${beneficiaryNameInput.value || 'غير محدد'}</strong></p>
                                <p>الهاتف: ${phoneNumberDropdown.value || newPhoneNumberInput.value || 'غير محدد'}</p>
                                <p>تاريخ التسديد: ${paymentDateInput.value ? new Date(paymentDateInput.value).toLocaleDateString('ar-IQ') : 'غير محدد'}</p>
                            </div>
                            <div class="print-from">
                                <h4>من:</h4>
                                <p><strong>شركة المشاريع الإنشائية</strong></p>
                                <p>العراق - بغداد</p>
                                <p>المضاف بواسطة: ${addedByInput.value || 'غير محدد'}</p>
                            </div>
                        </div>

                        <table class="print-table">
                            <thead>
                                <tr>
                                    <th>الوصف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>خرسانة ${cementTypeDropdown.value || 'غير محدد'} (${cementTypeText.value || 'غير محدد'})<br>
                                        <small>المقاومة: ${resistanceDropdown.value || 'غير محدد'}</small>
                                    </td>
                                    <td>${quantityInput.value || 0} م³</td>
                                    <td>${parseFloat(pricePerMeterInput.value || 0).toLocaleString()} د.ع</td>
                                    <td>${((parseFloat(quantityInput.value || 0)) * (parseFloat(pricePerMeterInput.value || 0))).toLocaleString()} د.ع</td>
                                </tr>
                                <tr>
                                    <td>أجرة البم</td>
                                    <td>1</td>
                                    <td>${parseFloat(pumpWageInput.value || 0).toLocaleString()} د.ع</td>
                                    <td>${parseFloat(pumpWageInput.value || 0).toLocaleString()} د.ع</td>
                                </tr>
                                <tr class="total-row">
                                    <td colspan="3"><strong>المجموع الإجمالي</strong></td>
                                    <td><strong>${parseFloat(totalAmountInput.value || 0).toLocaleString()} د.ع</strong></td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="print-summary">
                            <div class="print-notes">
                                <h4>التفاصيل والملاحظات:</h4>
                                <p><strong>التفاصيل:</strong> ${detailsInput.value || 'لا توجد تفاصيل'}</p>
                                <p><strong>الملاحظات:</strong> ${notesInput.value || 'لا توجد ملاحظات'}</p>
                            </div>
                            <div class="print-totals">
                                <div class="total-line">
                                    <span>المبلغ الإجمالي:</span>
                                    <span>${parseFloat(totalAmountInput.value || 0).toLocaleString()} د.ع</span>
                                </div>
                                <div class="total-line">
                                    <span>المبلغ الواصل:</span>
                                    <span>${parseFloat(receivedAmountInput.value || 0).toLocaleString()} د.ع</span>
                                </div>
                                <div class="total-line">
                                    <span>المبلغ المتبقي:</span>
                                    <span>${remainingAmount.toLocaleString()} د.ع</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Close preview modal if open
                if (previewModal.style.display === 'flex') {
                    closePreviewModal();
                }

                window.print();
                invoicePrintArea.innerHTML = '';
            }

            // New Professional Print Function
            // دالة الطباعة الجديدة - تطابق المعاينة تماماً
            function printProfessionalInvoice() {
                const currentDate = new Date();
                const invoiceNumber = `INV-${currentDate.getFullYear()}${(currentDate.getMonth() + 1).toString().padStart(2, '0')}${currentDate.getDate().toString().padStart(2, '0')}-${Date.now().toString().slice(-4)}`;
                const remainingAmount = (parseFloat(totalAmountInput.value) || 0) - (parseFloat(receivedAmountInput.value) || 0);
                const selectedEmployee = document.getElementById('employee-name').value;

                // نسخ نفس المحتوى من المعاينة بالضبط
                const printContent = `
                    <div class="preview-content">
                        <h2>فاتورة عمل</h2>
                        <div class="statement-header">
                            <div>
                                <h3>شركة المشاريع الإنشائية</h3>
                                <p>العراق - بغداد</p>
                                <p>الموظف المسؤول: ${selectedEmployee || 'غير محدد'}</p>
                            </div>
                            <div>
                                <h3>رقم الفاتورة: ${invoiceNumber}</h3>
                                <p>التاريخ: ${new Date(dateInput.value || new Date()).toLocaleDateString('ar-IQ')}</p>
                                <p>المستفيد: ${beneficiaryNameInput.value || 'غير محدد'}</p>
                                <p>الهاتف: ${phoneNumberDropdown.value || newPhoneNumberInput.value || 'غير محدد'}</p>
                            </div>
                        </div>

                        <table>
                            <thead>
                                <tr>
                                    <th>الوصف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>خرسانة ${cementTypeDropdown.value || 'غير محدد'} (${cementTypeText.value || 'غير محدد'})<br>
                                        المقاومة: ${resistanceDropdown.value || 'غير محدد'}
                                    </td>
                                    <td>${quantityInput.value || 0} م³</td>
                                    <td>${parseFloat(pricePerMeterInput.value || 0).toLocaleString()} د.ع</td>
                                    <td>${((parseFloat(quantityInput.value || 0)) * (parseFloat(pricePerMeterInput.value || 0))).toLocaleString()} د.ع</td>
                                </tr>
                                <tr>
                                    <td>أجرة البم</td>
                                    <td>1</td>
                                    <td>${parseFloat(pumpWageInput.value || 0).toLocaleString()} د.ع</td>
                                    <td>${parseFloat(pumpWageInput.value || 0).toLocaleString()} د.ع</td>
                                </tr>
                                ${addedByInput.value ? `
                                <tr>
                                    <td>${addedByInput.value}</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>مشمول</td>
                                </tr>
                                ` : ''}
                                <tr>
                                    <td colspan="3"><strong>المجموع الإجمالي</strong></td>
                                    <td><strong>${parseFloat(totalAmountInput.value || 0).toLocaleString()} د.ع</strong></td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="statement-totals">
                            <p><strong>التفاصيل:</strong> ${detailsInput.value || 'لا توجد تفاصيل'}</p>
                            <p><strong>الملاحظات:</strong> ${notesInput.value || 'لا توجد ملاحظات'}</p>
                            <p><strong>تاريخ التسديد:</strong> ${paymentDateInput.value ? new Date(paymentDateInput.value).toLocaleDateString('ar-IQ') : 'غير محدد'}</p>
                            <p>المبلغ الإجمالي: <strong>${parseFloat(totalAmountInput.value || 0).toLocaleString()} د.ع</strong></p>
                            <p>المبلغ الواصل: <strong>${parseFloat(receivedAmountInput.value || 0).toLocaleString()} د.ع</strong></p>
                            <p>المبلغ المتبقي: <strong>${remainingAmount.toLocaleString()} د.ع</strong></p>
                        </div>
                    </div>
                `;

                // إنشاء نافذة طباعة جديدة
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>فاتورة عمل</title>
                        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
                        <style>
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                            }
                            html {
                                direction: rtl;
                                lang: ar;
                            }
                            body {
                                font-family: 'Tajawal', sans-serif;
                                direction: rtl;
                                text-align: right;
                                unicode-bidi: embed;
                            }
                            .preview-content {
                                background: white !important;
                                color: #333 !important;
                                font-family: 'Tajawal', sans-serif;
                                direction: rtl;
                                text-align: right;
                                padding: 40px;
                                max-width: 210mm;
                                margin: 0 auto;
                                unicode-bidi: embed;
                            }
                            .preview-content h2 {
                                color: #667eea !important;
                                text-align: center;
                                margin-bottom: 30px;
                                font-size: 2rem;
                                font-weight: 700;
                            }
                            .preview-content .statement-header {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                margin-bottom: 30px;
                                padding-bottom: 20px;
                                border-bottom: 3px solid #667eea;
                                direction: rtl;
                                text-align: right;
                            }
                            .preview-content .statement-header h3 {
                                color: #333 !important;
                                font-size: 1.5rem;
                                margin: 0;
                            }
                            .preview-content .statement-header p {
                                color: #666 !important;
                                margin: 5px 0;
                                font-size: 1rem;
                            }
                            .preview-content table {
                                width: 100%;
                                border-collapse: collapse;
                                margin: 20px 0;
                                background: white;
                                direction: rtl;
                                text-align: right;
                            }
                            .preview-content table th {
                                background: #667eea !important;
                                color: white !important;
                                padding: 15px;
                                text-align: center;
                                font-weight: 700;
                                border: 1px solid #ddd;
                            }
                            .preview-content table td {
                                padding: 12px;
                                text-align: center;
                                border: 1px solid #ddd;
                                background: white !important;
                                color: #333 !important;
                            }
                            .preview-content .statement-totals {
                                margin-top: 30px;
                                padding-top: 20px;
                                border-top: 2px solid #ddd;
                                text-align: right;
                                direction: rtl;
                            }
                            .preview-content .statement-totals p {
                                font-size: 1.2rem;
                                font-weight: bold;
                                margin: 10px 0;
                                color: #333 !important;
                            }
                            .preview-content .statement-totals strong {
                                color: #667eea !important;
                            }
                            /* فوتر المبرمج */
                            .developer-footer {
                                position: fixed;
                                bottom: 10mm;
                                left: 0;
                                right: 0;
                                text-align: center;
                                font-size: 0.8rem;
                                color: #666;
                                border-top: 1px solid #ddd;
                                padding-top: 10px;
                                background: white;
                            }
                            .developer-footer p {
                                margin: 2px 0;
                                font-weight: 500;
                            }
                            .developer-footer .dev-name {
                                color: #667eea;
                                font-weight: 700;
                            }
                            .developer-footer .dev-phone {
                                color: #333;
                                font-weight: 600;
                            }
                            @media print {
                                @page { size: A4; margin: 15mm; }
                                body { -webkit-print-color-adjust: exact; }
                                .developer-footer {
                                    position: fixed;
                                    bottom: 5mm;
                                }
                            }
                        </style>
                    </head>
                    <body dir="rtl" lang="ar">
                        ${printContent}

                        <!-- فوتر المبرمج -->
                        <div class="developer-footer">
                            <p>تم تطوير هذا النظام بواسطة: <span class="dev-name">المبرمج أحمد علي</span></p>
                            <p>للتواصل: <span class="dev-phone">📱 07801234567</span></p>
                        </div>
                    </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.focus();
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            }

            // Expense Page
            const expenseDateInput = document.getElementById('expense-date');
            const expenseAmountInput = document.getElementById('expense-amount');
            const expenseDetailsInput = document.getElementById('expense-details');
            const expenseSourceInput = document.getElementById('expense-source');
            const expenseStatusDropdown = document.getElementById('expense-status');
            const addExpenseButton = document.getElementById('add-expense-button');
            const clearExpenseButton = document.getElementById('clear-expense-button');
            const expensesTableBody = document.querySelector('#expenses-table tbody');
            const totalExpensesSpan = document.getElementById('total-expenses');

            let expensesData = JSON.parse(localStorage.getItem('expensesData')) || [];

            addExpenseButton.addEventListener('click', () => {
                const data = {
                    date: expenseDateInput.value,
                    amount: parseFloat(expenseAmountInput.value) || 0,
                    details: expenseDetailsInput.value,
                    source: expenseSourceInput.value,
                    status: expenseStatusDropdown.value
                };

                expensesData.push(data);
                localStorage.setItem('expensesData', JSON.stringify(expensesData));
                displayDashboardExpensesData(expensesData);
                clearExpenseForm();
                alert('تمت إضافة المصروف بنجاح!');

                // تحديث الاقتراحات في حقل البحث إذا كان مفتوحاً
                if (document.getElementById('search-expense-source')) {
                    setupSearchAutocomplete('search-expense-source', 'search-expense-source-suggestions', getExpenseSources);
                }
            });

            clearExpenseButton.addEventListener('click', () => {
                expenseDateInput.value = new Date().toISOString().split('T')[0];
                expenseAmountInput.value = '';
                expenseDetailsInput.value = '';
                expenseSourceInput.value = '';
                expenseStatusDropdown.value = 'غير مسدد';
            });

            // زر تصدير Excel للمصاريف
            const exportExpensesButton = document.getElementById('export-expenses-button');
            if (exportExpensesButton) {
                exportExpensesButton.addEventListener('click', () => {
                    exportExpensesToExcel();
                });
            }

            function exportExpensesToExcel() {
                if (expensesData.length === 0) {
                    alert('لا توجد بيانات مصاريف للتصدير');
                    return;
                }

                const dataToExport = expensesData.map(expense => ({
                    'التاريخ': expense.date,
                    'المبلغ (د.ع)': expense.amount,
                    'التفاصيل': expense.details,
                    'جهة الصرف': expense.source,
                    'الحالة': expense.status
                }));

                const worksheet = XLSX.utils.json_to_sheet(dataToExport);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, "المصاريف");

                // إضافة صف المجاميع
                const totalAmount = expensesData.reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
                const paidAmount = expensesData.filter(e => e.status === 'مسدد').reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
                const unpaidAmount = totalAmount - paidAmount;

                XLSX.utils.sheet_add_json(worksheet, [
                    { 'التاريخ': '', 'المبلغ (د.ع)': '', 'التفاصيل': '', 'جهة الصرف': '', 'الحالة': '' },
                    { 'التاريخ': 'إجمالي المصاريف', 'المبلغ (د.ع)': totalAmount, 'التفاصيل': '', 'جهة الصرف': '', 'الحالة': '' },
                    { 'التاريخ': 'المبلغ المسدد', 'المبلغ (د.ع)': paidAmount, 'التفاصيل': '', 'جهة الصرف': '', 'الحالة': '' },
                    { 'التاريخ': 'المبلغ غير المسدد', 'المبلغ (د.ع)': unpaidAmount, 'التفاصيل': '', 'جهة الصرف': '', 'الحالة': '' }
                ], { origin: -1, skipHeader: true });

                const fileName = `المصاريف_${new Date().toISOString().split('T')[0]}.xlsx`;
                XLSX.writeFile(workbook, fileName);
            }

            const dashboardWorkStatusTableBody = document.getElementById('dashboard-work-status-table-body');
            const dashboardExpensesTableBody = document.getElementById('dashboard-expenses-table-body');
            const applyWorkStatusFilterBtn = document.getElementById('apply-work-status-filter');
            const clearWorkStatusFilterBtn = document.getElementById('clear-work-status-filter');
            const downloadWorkStatusExcelBtn = document.getElementById('download-work-status-excel');
            const printStatementBtn = document.getElementById('print-statement-button');
            const statementPrintArea = document.getElementById('statement-print-area');

            function displayDashboardWorkStatusData(dataToDisplay) {
                currentFilteredWorkStatus = dataToDisplay; // Store filtered data
                dashboardWorkStatusTableBody.innerHTML = '';
                let totalAmountSum = 0;
                let totalReceivedSum = 0;
                let totalRemainingSum = 0;

                dataToDisplay.forEach((data, index) => {
                    const row = dashboardWorkStatusTableBody.insertRow();
                    const totalAmount = parseFloat(data.totalAmount) || 0;
                    const receivedAmount = parseFloat(data.receivedAmount) || 0;
                    const remaining = totalAmount - receivedAmount;

                    // البحث عن الفهرس الأصلي في البيانات الكاملة
                    const originalIndex = workStatusData.findIndex(item =>
                        item.date === data.date &&
                        item.beneficiaryName === data.beneficiaryName &&
                        item.totalAmount === data.totalAmount &&
                        item.receivedAmount === data.receivedAmount
                    );

                    // تحديد حالة السداد وإضافة فئة CSS مناسبة
                    if (receivedAmount > 0 && receivedAmount === totalAmount) {
                        // مسدد: المبلغ الواصل = المبلغ الكلي
                        row.classList.add('paid-expense');
                    } else if (receivedAmount === 0) {
                        // غير مسدد: المبلغ الواصل فارغ أو صفر
                        row.classList.add('unpaid-expense');
                    } else if (receivedAmount > 0 && receivedAmount !== totalAmount) {
                        // المتبقي: المبلغ الواصل ≠ المبلغ الكلي
                        row.classList.add('partial-expense');
                    }

                    totalAmountSum += totalAmount;
                    totalReceivedSum += receivedAmount;
                    totalRemainingSum += remaining;

                    // عمود الإجراءات منقول للعمود الأول
                    row.innerHTML = `
                        <td style="background: rgba(72, 187, 120, 0.1); min-width: 150px;">
                            <button class="edit-button" data-index="${originalIndex}">${appSettings.editButtonText}</button>
                            <button class="delete-button" data-index="${originalIndex}">${appSettings.deleteButtonText}</button>
                        </td>
                        <td>${data.date}</td>
                        <td>${data.quantity}</td>
                        <td>${data.cementTypeText}</td>
                        <td>${parseFloat(data.pricePerMeter).toLocaleString()}</td>
                        <td>${parseFloat(data.pumpWage).toLocaleString()}</td>
                        <td>${totalAmount.toLocaleString()}</td>
                        <td>${receivedAmount.toLocaleString()}</td>
                        <td>${remaining.toLocaleString()}</td>
                        <td>${data.cementTypeDropdown}</td>
                        <td>${data.resistance}</td>
                        <td>${data.beneficiaryName}</td>
                        <td>${data.details}</td>
                        <td>${data.addedBy}</td>
                        <td>${data.employeeName}</td>
                        <td>${data.paymentDate}</td>
                        <td>${data.phoneNumber}</td>
                        <td>${data.notes}</td>
                    `;
                });
                document.getElementById('total-amount-sum').textContent = totalAmountSum.toLocaleString();
                document.getElementById('total-received-sum').textContent = totalReceivedSum.toLocaleString();
                document.getElementById('total-remaining-sum').textContent = totalRemainingSum.toLocaleString();

                // إضافة event listeners لأزرار التعديل والحذف
                addWorkStatusTableEventListeners();
            }

            function addWorkStatusTableEventListeners() {
                // إضافة event listeners لأزرار التعديل
                document.querySelectorAll('#dashboard-work-status-table .edit-button').forEach(button => {
                    button.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        editWorkStatusItem(index);
                    });
                });

                // إضافة event listeners لأزرار الحذف
                document.querySelectorAll('#dashboard-work-status-table .delete-button').forEach(button => {
                    button.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        deleteWorkStatusItem(index);
                    });
                });
            }

            function editWorkStatusItem(index) {
                if (index >= 0 && index < workStatusData.length) {
                    const item = workStatusData[index];

                    // ملء النموذج بالبيانات الحالية
                    dateInput.value = item.date;
                    quantityInput.value = item.quantity;
                    cementTypeText.value = item.cementTypeText;
                    pricePerMeterInput.value = item.pricePerMeter;
                    pumpWageInput.value = item.pumpWage;
                    totalAmountInput.value = item.totalAmount;
                    receivedAmountInput.value = item.receivedAmount;
                    cementTypeDropdown.value = item.cementTypeDropdown;
                    resistanceDropdown.value = item.resistance;
                    beneficiaryNameInput.value = item.beneficiaryName;
                    detailsInput.value = item.details;
                    addedByInput.value = item.addedBy;
                    document.getElementById('employee-name').value = item.employeeName;
                    paymentDateInput.value = item.paymentDate;
                    phoneNumberDropdown.value = item.phoneNumber;
                    newPhoneNumberInput.value = item.phoneNumber;
                    notesInput.value = item.notes;

                    // حذف العنصر القديم
                    workStatusData.splice(index, 1);
                    localStorage.setItem('workStatusData', JSON.stringify(workStatusData));

                    // تحديث الجدول
                    displayDashboardWorkStatusData(workStatusData);

                    // الانتقال إلى صفحة موقف العمل للتعديل
                    showPage(workStatusPage);

                    alert('تم تحميل البيانات للتعديل. يرجى إجراء التعديلات المطلوبة ثم الضغط على إضافة.');
                }
            }

            function deleteWorkStatusItem(index) {
                if (index >= 0 && index < workStatusData.length) {
                    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                        workStatusData.splice(index, 1);
                        localStorage.setItem('workStatusData', JSON.stringify(workStatusData));
                        displayDashboardWorkStatusData(workStatusData);
                        alert('تم حذف العنصر بنجاح!');
                    }
                }
            }

            function applyWorkStatusFilter() {
                const searchBeneficiary = document.getElementById('search-beneficiary').value.toLowerCase();
                const searchPaidStatus = document.getElementById('search-paid-status').value;
                const searchDateFrom = document.getElementById('search-date-from').value;
                const searchDateTo = document.getElementById('search-date-to').value;

                let filteredData = workStatusData.filter(item => {
                    const totalAmount = parseFloat(item.totalAmount) || 0;
                    const receivedAmount = parseFloat(item.receivedAmount) || 0;

                    // تحديد حالة السداد حسب المتطلبات الجديدة
                    const isPaid = receivedAmount > 0 && receivedAmount === totalAmount; // مسدد: المبلغ الواصل = المبلغ الكلي
                    const isUnpaid = receivedAmount === 0; // غير مسدد: المبلغ الواصل فارغ أو صفر
                    const isPartial = receivedAmount > 0 && receivedAmount !== totalAmount; // المتبقي: المبلغ الواصل ≠ المبلغ الكلي

                    const beneficiaryMatch = !searchBeneficiary || (item.beneficiaryName && item.beneficiaryName.toLowerCase().includes(searchBeneficiary));
                    const dateFromMatch = !searchDateFrom || item.date >= searchDateFrom;
                    const dateToMatch = !searchDateTo || item.date <= searchDateTo;

                    let paidStatusMatch = true;
                    if (searchPaidStatus === "paid") {
                        paidStatusMatch = isPaid;
                    } else if (searchPaidStatus === "unpaid") {
                        paidStatusMatch = isUnpaid;
                    } else if (searchPaidStatus === "partial") {
                        paidStatusMatch = isPartial;
                    }

                    return beneficiaryMatch && dateFromMatch && dateToMatch && paidStatusMatch;
                });

                displayDashboardWorkStatusData(filteredData);
            }

            function clearWorkStatusFilter() {
                document.getElementById('search-beneficiary').value = '';
                document.getElementById('search-paid-status').value = '';
                document.getElementById('search-date-from').value = '';
                document.getElementById('search-date-to').value = '';
                displayDashboardWorkStatusData(workStatusData);
            }

            function downloadFilteredWorkStatusAsExcel() {
                const dataToExport = currentFilteredWorkStatus.map(d => ({
                    'التاريخ': d.date,
                    'الكمية': d.quantity,
                    'السمنت': d.cementTypeText,
                    'سعر المتر': d.pricePerMeter,
                    'أجرة البم': d.pumpWage,
                    'المبلغ': d.totalAmount,
                    'الواصل': d.receivedAmount,
                    'المتبقي': (d.totalAmount || 0) - (d.receivedAmount || 0),
                    'نوع السمنت': d.cementTypeDropdown,
                    'المقاومة': d.resistance,
                    'اسم المستفيد': d.beneficiaryName,
                    'التفاصيل': d.details,
                    'تاريخ التسديد': d.paymentDate,
                    'رقم الهاتف': d.phoneNumber,
                    'الملاحظات': d.notes
                }));

                const worksheet = XLSX.utils.json_to_sheet(dataToExport);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, "كشف حساب");

                XLSX.writeFile(workbook, "كشف_حساب_موقف_العمل.xlsx");
            }

            function printStatement() {
                 if (currentFilteredWorkStatus.length === 0) {
                    alert("لا توجد بيانات لطباعة. الرجاء تطبيق البحث أولاً.");
                    return;
                }

                // جمع تفاصيل البحث المطبق
                const searchBeneficiary = document.getElementById('search-beneficiary').value;
                const searchPaidStatus = document.getElementById('search-paid-status').value;
                const searchDateFrom = document.getElementById('search-date-from').value;
                const searchDateTo = document.getElementById('search-date-to').value;

                let totalAmount = 0;
                let totalReceived = 0;

                // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
                const sortedData = [...currentFilteredWorkStatus].sort((a, b) => new Date(b.date) - new Date(a.date));

                // إنشاء نافذة طباعة جديدة
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>كشف حساب موقف العمل</title>
                        <style>
                            @page {
                                size: A4 landscape;
                                margin: 15mm;
                            }

                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                            }

                            body {
                                font-family: 'Arial', sans-serif;
                                direction: rtl;
                                font-size: 12px;
                                line-height: 1.4;
                                color: #333;
                            }

                            .header {
                                text-align: center;
                                margin-bottom: 20px;
                                border-bottom: 3px solid #667eea;
                                padding-bottom: 15px;
                            }

                            .header h1 {
                                color: #667eea;
                                font-size: 24px;
                                font-weight: bold;
                                margin-bottom: 10px;
                            }

                            .company-info {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: 15px;
                                background: #f8fafc;
                                padding: 15px;
                                border-radius: 8px;
                            }

                            .search-details {
                                background: #e2e8f0;
                                padding: 10px;
                                border-radius: 5px;
                                margin-bottom: 15px;
                            }

                            .search-details h3 {
                                color: #4a5568;
                                margin-bottom: 8px;
                                font-size: 14px;
                            }

                            .search-details p {
                                margin: 3px 0;
                                font-size: 11px;
                            }

                            table {
                                width: 100%;
                                border-collapse: collapse;
                                margin: 15px 0;
                                font-size: 10px;
                            }

                            th {
                                background: #667eea;
                                color: white;
                                padding: 8px 4px;
                                text-align: center;
                                font-weight: bold;
                                border: 1px solid #ddd;
                                font-size: 9px;
                            }

                            td {
                                padding: 6px 4px;
                                text-align: center;
                                border: 1px solid #ddd;
                                font-size: 9px;
                            }

                            .paid-row {
                                background-color: rgba(72, 187, 120, 0.1);
                                color: #2f855a;
                            }

                            .unpaid-row {
                                background-color: rgba(245, 101, 101, 0.1);
                                color: #c53030;
                            }

                            .partial-row {
                                background-color: rgba(237, 137, 54, 0.1);
                                color: #c05621;
                            }

                            .totals {
                                background: #f8fafc;
                                padding: 15px;
                                border-radius: 8px;
                                margin-top: 15px;
                                border: 2px solid #667eea;
                            }

                            .totals h3 {
                                color: #667eea;
                                margin-bottom: 10px;
                                text-align: center;
                            }

                            .totals-grid {
                                display: grid;
                                grid-template-columns: 1fr 1fr 1fr;
                                gap: 15px;
                                text-align: center;
                            }

                            .total-item {
                                background: white;
                                padding: 10px;
                                border-radius: 5px;
                                border: 1px solid #e2e8f0;
                            }

                            .total-item strong {
                                color: #667eea;
                                font-size: 14px;
                            }

                            @media print {
                                body { -webkit-print-color-adjust: exact; }
                                .paid-row { background-color: rgba(72, 187, 120, 0.1) !important; }
                                .unpaid-row { background-color: rgba(245, 101, 101, 0.1) !important; }
                                .partial-row { background-color: rgba(237, 137, 54, 0.1) !important; }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>كشف حساب موقف العمل</h1>
                        </div>

                        <div class="company-info">
                            <div>
                                <h3>شركة المشاريع الإنشائية</h3>
                                <p>العراق - بغداد</p>
                                <p>عدد السجلات: ${sortedData.length}</p>
                            </div>
                            <div>
                                <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-IQ')}</p>
                                <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-IQ')}</p>
                            </div>
                        </div>`);

                // إضافة تفاصيل البحث
                let searchInfo = '';
                if (searchBeneficiary || searchPaidStatus || searchDateFrom || searchDateTo) {
                    let statusText = 'الكل';
                    if (searchPaidStatus === 'paid') statusText = 'مسدد ✅';
                    else if (searchPaidStatus === 'unpaid') statusText = 'غير مسدد ❌';
                    else if (searchPaidStatus === 'partial') statusText = 'المتبقي ⚠️';

                    searchInfo = `
                        <div class="search-details">
                            <h3>🔍 تفاصيل البحث المطبق:</h3>
                            ${searchBeneficiary ? `<p><strong>المستفيد:</strong> ${searchBeneficiary}</p>` : ''}
                            <p><strong>حالة السداد:</strong> ${statusText}</p>
                            ${searchDateFrom ? `<p><strong>من تاريخ:</strong> ${searchDateFrom}</p>` : ''}
                            ${searchDateTo ? `<p><strong>إلى تاريخ:</strong> ${searchDateTo}</p>` : ''}
                        </div>
                    `;
                }

                printWindow.document.write(searchInfo);

                // إنشاء صفوف الجدول مع جميع الأعمدة
                const tableRows = sortedData.map(d => {
                    const remaining = (d.totalAmount || 0) - (d.receivedAmount || 0);
                    totalAmount += (d.totalAmount || 0);
                    totalReceived += (d.receivedAmount || 0);

                    // تحديد فئة CSS للحالة
                    const totalAmt = parseFloat(d.totalAmount) || 0;
                    const receivedAmt = parseFloat(d.receivedAmount) || 0;
                    let rowClass = '';
                    if (receivedAmt > 0 && receivedAmt === totalAmt) {
                        rowClass = 'paid-row';
                    } else if (receivedAmt === 0) {
                        rowClass = 'unpaid-row';
                    } else if (receivedAmt > 0 && receivedAmt !== totalAmt) {
                        rowClass = 'partial-row';
                    }

                    return `
                        <tr class="${rowClass}">
                            <td>${d.date || '-'}</td>
                            <td>${d.quantity || '-'}</td>
                            <td>${d.cementTypeText || '-'}</td>
                            <td>${(d.pricePerMeter || 0).toLocaleString()}</td>
                            <td>${(d.pumpWage || 0).toLocaleString()}</td>
                            <td>${(d.totalAmount || 0).toLocaleString()}</td>
                            <td>${(d.receivedAmount || 0).toLocaleString()}</td>
                            <td>${d.cementTypeDropdown || '-'}</td>
                            <td>${d.resistance || '-'}</td>
                            <td>${d.beneficiaryName || '-'}</td>
                            <td>${d.details || '-'}</td>
                            <td>${d.addedBy || '-'}</td>
                            <td>${d.paymentDate || '-'}</td>
                            <td>${d.phoneNumber || '-'}</td>
                            <td>${d.notes || '-'}</td>
                            <td>${remaining.toLocaleString()}</td>
                        </tr>
                    `;
                }).join('');

                const totalRemaining = totalAmount - totalReceived;

                // إنشاء الجدول الكامل
                printWindow.document.write(`
                        <table>
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الكمية</th>
                                    <th>السمنت</th>
                                    <th>سعر المتر</th>
                                    <th>أجرة البم</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ الواصل</th>
                                    <th>نوع السمنت</th>
                                    <th>المقاومة</th>
                                    <th>اسم المستفيد</th>
                                    <th>التفاصيل</th>
                                    <th>المضاف</th>
                                    <th>تاريخ التسديد</th>
                                    <th>رقم الهاتف</th>
                                    <th>الملاحظات</th>
                                    <th>المتبقي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>

                        <div class="totals">
                            <h3>📊 ملخص المجاميع</h3>
                            <div class="totals-grid">
                                <div class="total-item">
                                    <p>مجموع المبالغ</p>
                                    <strong>${totalAmount.toLocaleString()} د.ع</strong>
                                </div>
                                <div class="total-item">
                                    <p>مجموع الواصل</p>
                                    <strong>${totalReceived.toLocaleString()} د.ع</strong>
                                </div>
                                <div class="total-item">
                                    <p>إجمالي المتبقي</p>
                                    <strong>${totalRemaining.toLocaleString()} د.ع</strong>
                                </div>
                            </div>
                        </div>

                        <!-- فوتر المبرمج -->
                        <div style="position: fixed; bottom: 5mm; left: 0; right: 0; text-align: center; font-size: 0.8rem; color: #666; border-top: 1px solid #ddd; padding-top: 10px; background: white;">
                            <p style="margin: 2px 0; font-weight: 500;">تم تطوير هذا النظام بواسطة: <span style="color: #667eea; font-weight: 700;">المبرمج أحمد علي</span></p>
                            <p style="margin: 2px 0; font-weight: 500;">للتواصل: <span style="color: #333; font-weight: 600;">📱 07801234567</span></p>
                        </div>
                    </body>
                    </html>
                `);

                printWindow.document.close();
                printWindow.focus();

                // طباعة تلقائية بعد تحميل المحتوى
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            }

            // تحسين زر تطبيق البحث مع تأثيرات بصرية
            applyWorkStatusFilterBtn.addEventListener('click', function() {
                // تأثير بصري للزر
                this.classList.add('loading');

                setTimeout(() => {
                    applyWorkStatusFilter();
                    this.classList.remove('loading');

                    // رسالة تأكيد
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i><span>تم التطبيق</span>';

                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);
                }, 800);
            });
            // تحسين زر مسح البحث مع تأثيرات بصرية
            clearWorkStatusFilterBtn.addEventListener('click', function() {
                // تأثير بصري للزر
                this.classList.add('loading');

                setTimeout(() => {
                    clearWorkStatusFilter();
                    this.classList.remove('loading');

                    // رسالة تأكيد
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i><span>تم المسح</span>';

                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);
                }, 600);
            });
            downloadWorkStatusExcelBtn.addEventListener('click', downloadFilteredWorkStatusAsExcel);
            printStatementBtn.addEventListener('click', printStatement);

            // إضافة وظيفة زر إعادة التعيين الجديد
            const resetAllFiltersBtn = document.getElementById('reset-all-filters');
            if (resetAllFiltersBtn) {
                resetAllFiltersBtn.addEventListener('click', function() {
                    // إعادة تعيين جميع حقول البحث
                    document.getElementById('search-beneficiary').value = '';
                    document.getElementById('search-paid-status').value = '';
                    document.getElementById('search-date-from').value = '';
                    document.getElementById('search-date-to').value = '';

                    // تأثير بصري للزر
                    this.classList.add('loading');

                    setTimeout(() => {
                        // إعادة عرض جميع البيانات
                        displayDashboardWorkStatusData(workStatusData);
                        currentFilteredWorkStatus = [...workStatusData];

                        // إزالة تأثير التحميل
                        this.classList.remove('loading');

                        // رسالة تأكيد
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-check"></i><span>تم إعادة التعيين</span>';

                        setTimeout(() => {
                            this.innerHTML = originalText;
                        }, 2000);
                    }, 1000);
                });
            }

            function displayDashboardExpensesData(dataToDisplay) {
                const allTables = [
                    document.querySelector('#expenses-table tbody'),
                    document.getElementById('dashboard-expenses-table-body')
                ];

                allTables.forEach(tbody => {
                    if (tbody) tbody.innerHTML = '';
                });

                let totalAmount = 0;
                let paidAmount = 0;
                let unpaidAmount = 0;

                dataToDisplay.forEach((data, index) => {
                    const amount = parseFloat(data.amount) || 0;
                    totalAmount += amount;

                    // حساب المبالغ حسب الحالة
                    if (data.status === 'مسدد') {
                        paidAmount += amount;
                    } else {
                        unpaidAmount += amount;
                    }

                    allTables.forEach(tbody => {
                        if (!tbody) return;
                        const row = tbody.insertRow();

                        // إضافة فئة CSS للمصاريف المسددة
                        if (data.status === 'مسدد') {
                            row.classList.add('paid-expense');
                        }

                        row.innerHTML = `
                            <td style="background: rgba(72, 187, 120, 0.1); min-width: 150px;">
                                <button class="edit-button" data-index="${index}">${appSettings.editButtonText}</button>
                                <button class="delete-button" data-index="${index}">${appSettings.deleteButtonText}</button>
                            </td>
                            <td>${data.date}</td>
                            <td>${data.amount.toLocaleString()}</td>
                            <td>${data.details}</td>
                            <td>${data.source}</td>
                            <td>
                                ${data.status === 'مسدد' ?
                                    '<span style="color: #38a169; font-weight: bold;">✅ مسدد</span>' :
                                    '<span style="color: #e53e3e; font-weight: bold;">❌ غير مسدد</span>'
                                }
                            </td>
                        `;
                    });
                });

                // تحديث المجاميع في صفحة المصاريف
                if (totalExpensesSpan) totalExpensesSpan.textContent = totalAmount.toLocaleString();
                const paidExpensesSpan = document.getElementById('paid-expenses');
                const unpaidExpensesSpan = document.getElementById('unpaid-expenses');
                if (paidExpensesSpan) paidExpensesSpan.textContent = paidAmount.toLocaleString();
                if (unpaidExpensesSpan) unpaidExpensesSpan.textContent = unpaidAmount.toLocaleString();

                // تحديث المجموع في لوحة التحكم
                const dashboardTotalExpenses = document.getElementById('dashboard-total-expenses');
                if (dashboardTotalExpenses) dashboardTotalExpenses.textContent = totalAmount.toLocaleString();

                // إضافة event listeners لأزرار التعديل والحذف
                addExpensesTableEventListeners();
            }

            function addExpensesTableEventListeners() {
                // إضافة event listeners لأزرار التعديل
                document.querySelectorAll('#expenses-table .edit-button').forEach(button => {
                    button.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        editExpenseItem(index);
                    });
                });

                // إضافة event listeners لأزرار الحذف
                document.querySelectorAll('#expenses-table .delete-button').forEach(button => {
                    button.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        deleteExpenseItem(index);
                    });
                });
            }

            function editExpenseItem(index) {
                if (index >= 0 && index < expensesData.length) {
                    const item = expensesData[index];

                    // ملء النموذج بالبيانات الحالية
                    expenseDateInput.value = item.date;
                    expenseAmountInput.value = item.amount;
                    expenseDetailsInput.value = item.details;
                    expenseSourceInput.value = item.source;
                    expenseStatusDropdown.value = item.status;

                    // حذف العنصر القديم
                    expensesData.splice(index, 1);
                    localStorage.setItem('expensesData', JSON.stringify(expensesData));

                    // تحديث الجدول
                    displayDashboardExpensesData(expensesData);

                    alert('تم تحميل البيانات للتعديل. يرجى إجراء التعديلات المطلوبة ثم الضغط على إضافة.');
                }
            }

            function deleteExpenseItem(index) {
                if (index >= 0 && index < expensesData.length) {
                    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
                        expensesData.splice(index, 1);
                        localStorage.setItem('expensesData', JSON.stringify(expensesData));
                        displayDashboardExpensesData(expensesData);
                        alert('تم حذف المصروف بنجاح!');
                    }
                }
            }

            // CREDENTIALS MANAGEMENT
            const addCredsBtn = document.getElementById('add-credentials-button-portal');
            const credsTableBody = document.getElementById('credentials-table-body');

            function displayCredentials() {
                credsTableBody.innerHTML = '';
                userCredentials.forEach(cred => {
                    const row = credsTableBody.insertRow();
                    row.innerHTML = `
                        <td>${cred.username}</td>
                        <td>${'*'.repeat(cred.password.length)}</td>
                        <td>
                            <button class="delete-button" data-username="${cred.username}">${appSettings.deleteButtonText}</button>
                        </td>
                    `;
                });
            }

            addCredsBtn.addEventListener('click', () => {
                const newUsername = document.getElementById('add-username-cred').value.trim();
                const newPassword = document.getElementById('add-password-cred').value.trim();
                if (newUsername && newPassword) {
                    if (userCredentials.some(c => c.username === newUsername)) {
                        alert('اسم المستخدم موجود بالفعل.');
                        return;
                    }
                    userCredentials.push({ username: newUsername, password: newPassword });
                    localStorage.setItem('userCredentials', JSON.stringify(userCredentials));
                    displayCredentials();
                    document.getElementById('add-username-cred').value = '';
                    document.getElementById('add-password-cred').value = '';
                    alert('تمت إضافة الحساب بنجاح.');
                } else {
                    alert('الرجاء إدخال اسم مستخدم وكلمة مرور.');
                }
            });

            credsTableBody.addEventListener('click', (e) => {
                if (e.target.classList.contains('delete-button')) {
                    const usernameToDelete = e.target.dataset.username;
                    if (confirm(`هل أنت متأكد من حذف المستخدم ${usernameToDelete}?`)) {
                        userCredentials = userCredentials.filter(c => c.username !== usernameToDelete);
                        localStorage.setItem('userCredentials', JSON.stringify(userCredentials));
                        displayCredentials();
                    }
                }
            });

            // CONTACTS MANAGEMENT
            const addContactBtn = document.getElementById('add-contact-button');
            const contactsTableBody = document.getElementById('phone-numbers-table-body');
            let editingContactIndex = -1;

            function displayContactsData() {
                contactsTableBody.innerHTML = '';
                savedContacts.forEach((contact, index) => {
                    const row = contactsTableBody.insertRow();
                    row.innerHTML = `
                        <td>${contact.name}</td>
                        <td>${contact.phone}</td>
                        <td>
                             <button class="edit-button" data-index="${index}">${appSettings.editButtonText}</button>
                             <button class="delete-button" data-index="${index}">${appSettings.deleteButtonText}</button>
                        </td>
                    `;
                });
            }

            function populatePhoneNumbers() {
                phoneNumberDropdown.innerHTML = '<option value="">اختر اسم أو أدخل رقم جديد</option>';
                savedContacts.forEach(contact => {
                    const option = document.createElement('option');
                    option.value = contact.phone;
                    option.textContent = contact.name;
                    option.dataset.phone = contact.phone;
                    phoneNumberDropdown.appendChild(option);
                });
            }

            addContactBtn.addEventListener('click', () => {
                const nameInput = document.getElementById('contact-name');
                const phoneInput = document.getElementById('contact-phone');
                const name = nameInput.value.trim();
                const phone = phoneInput.value.trim();

                if (name && phone) {
                    if (editingContactIndex > -1) {
                        // Update existing contact
                        savedContacts[editingContactIndex] = { name, phone };
                        addContactBtn.textContent = 'إضافة جهة اتصال';
                        editingContactIndex = -1;
                    } else {
                        // Add new contact
                        savedContacts.push({ name, phone });
                    }
                    localStorage.setItem('savedContacts', JSON.stringify(savedContacts));
                    displayContactsData();
                    populatePhoneNumbers();
                    nameInput.value = '';
                    phoneInput.value = '';
                } else {
                    alert('الرجاء إدخال الاسم ورقم الهاتف.');
                }
            });

            contactsTableBody.addEventListener('click', (e) => {
                const index = e.target.dataset.index;
                if (e.target.classList.contains('delete-button')) {
                    if (confirm(`هل أنت متأكد من حذف جهة الاتصال هذه؟`)) {
                        savedContacts.splice(index, 1);
                        localStorage.setItem('savedContacts', JSON.stringify(savedContacts));
                        displayContactsData();
                        populatePhoneNumbers();
                    }
                } else if (e.target.classList.contains('edit-button')) {
                    const contact = savedContacts[index];
                    document.getElementById('contact-name').value = contact.name;
                    document.getElementById('contact-phone').value = contact.phone;
                    editingContactIndex = index;
                    addContactBtn.textContent = 'تحديث جهة الاتصال';
                }
            });

            // GENERAL EDIT/DELETE LISTENERS (Event Delegation)
            document.body.addEventListener('click', (e) => {
                const target = e.target;
                const index = target.dataset.index;
                if (index === undefined) return;

                // WORK STATUS TABLE
                const workStatusTable = target.closest('#dashboard-work-status-table-body');
                if (workStatusTable) {
                    if (target.classList.contains('delete-button')) {
                         if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
                            workStatusData.splice(index, 1);
                            localStorage.setItem('workStatusData', JSON.stringify(workStatusData));
                            applyWorkStatusFilter();
                        }
                    } else if (target.classList.contains('edit-button')) {
                        editingWorkStatusIndex = index;
                        createWorkStatusEditForm(workStatusData[index], index);
                        openEditModal();
                    }
                }

                // EXPENSES TABLES
                const expensesTable = target.closest('#expenses-table, #dashboard-expenses-table');
                if (expensesTable) {
                    if (target.classList.contains('delete-button')) {
                         if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
                            expensesData.splice(index, 1);
                            localStorage.setItem('expensesData', JSON.stringify(expensesData));
                            displayDashboardExpensesData(expensesData); // Re-render both tables
                        }
                    } else if (target.classList.contains('edit-button')) {
                        editingExpenseIndex = index;
                        createExpenseEditForm(expensesData[index], index);
                        openEditModal();
                    }
                }
            });

            let editingWorkStatusIndex = -1;
            let editingExpenseIndex = -1;
            const editModal = document.getElementById('edit-modal');
            const modalBody = document.getElementById('modal-body');
            const modalCloseButton = document.getElementById('modal-close-button');

            // Preview Modal Elements
            const previewModal = document.getElementById('preview-modal');
            const previewModalBody = document.getElementById('preview-modal-body');
            const previewModalClose = document.getElementById('preview-modal-close');

            function openEditModal() {
                editModal.style.display = 'flex';
            }

            function closeEditModal() {
                editModal.style.display = 'none';
                editingWorkStatusIndex = -1;
                editingExpenseIndex = -1;
                modalBody.innerHTML = '';
            }

            modalCloseButton.addEventListener('click', closeEditModal);
            editModal.addEventListener('click', (e) => {
                if (e.target === editModal) closeEditModal();
            });

            // Preview Modal Functions
            function openPreviewModal() {
                previewModal.style.display = 'flex';
            }

            function closePreviewModal() {
                previewModal.style.display = 'none';
                previewModalBody.innerHTML = '';
            }

            previewModalClose.addEventListener('click', closePreviewModal);
            previewModal.addEventListener('click', (e) => {
                if (e.target === previewModal) closePreviewModal();
            });

            // Show Invoice Preview Function
            function showInvoicePreview() {
                const currentDate = new Date();
                const invoiceNumber = `INV-${currentDate.getFullYear()}${(currentDate.getMonth() + 1).toString().padStart(2, '0')}${currentDate.getDate().toString().padStart(2, '0')}-${Date.now().toString().slice(-4)}`;
                const remainingAmount = (parseFloat(totalAmountInput.value) || 0) - (parseFloat(receivedAmountInput.value) || 0);
                const selectedEmployee = document.getElementById('employee-name').value;

                previewModalBody.innerHTML = `
                    <div class="preview-content">
                        <h2>فاتورة عمل</h2>
                        <div class="statement-header">
                            <div>
                                <h3>شركة المشاريع الإنشائية</h3>
                                <p>العراق - بغداد</p>
                                <p>الموظف المسؤول: ${selectedEmployee || 'غير محدد'}</p>
                            </div>
                            <div>
                                <h3>رقم الفاتورة: ${invoiceNumber}</h3>
                                <p>التاريخ: ${new Date(dateInput.value || new Date()).toLocaleDateString('ar-IQ')}</p>
                                <p>المستفيد: ${beneficiaryNameInput.value || 'غير محدد'}</p>
                                <p>الهاتف: ${phoneNumberDropdown.value || newPhoneNumberInput.value || 'غير محدد'}</p>
                            </div>
                        </div>

                        <table>
                            <thead>
                                <tr>
                                    <th>الوصف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>خرسانة ${cementTypeDropdown.value || 'غير محدد'} (${cementTypeText.value || 'غير محدد'})<br>
                                        المقاومة: ${resistanceDropdown.value || 'غير محدد'}
                                    </td>
                                    <td>${quantityInput.value || 0} م³</td>
                                    <td>${parseFloat(pricePerMeterInput.value || 0).toLocaleString()} د.ع</td>
                                    <td>${((parseFloat(quantityInput.value || 0)) * (parseFloat(pricePerMeterInput.value || 0))).toLocaleString()} د.ع</td>
                                </tr>
                                <tr>
                                    <td>أجرة البم</td>
                                    <td>1</td>
                                    <td>${parseFloat(pumpWageInput.value || 0).toLocaleString()} د.ع</td>
                                    <td>${parseFloat(pumpWageInput.value || 0).toLocaleString()} د.ع</td>
                                </tr>
                                ${addedByInput.value ? `
                                <tr>
                                    <td>${addedByInput.value}</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>مشمول</td>
                                </tr>
                                ` : ''}
                                <tr>
                                    <td colspan="3"><strong>المجموع الإجمالي</strong></td>
                                    <td><strong>${parseFloat(totalAmountInput.value || 0).toLocaleString()} د.ع</strong></td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="statement-totals">
                            <p><strong>التفاصيل:</strong> ${detailsInput.value || 'لا توجد تفاصيل'}</p>
                            <p><strong>الملاحظات:</strong> ${notesInput.value || 'لا توجد ملاحظات'}</p>
                            <p><strong>تاريخ التسديد:</strong> ${paymentDateInput.value ? new Date(paymentDateInput.value).toLocaleDateString('ar-IQ') : 'غير محدد'}</p>
                            <p>المبلغ الإجمالي: <strong>${parseFloat(totalAmountInput.value || 0).toLocaleString()} د.ع</strong></p>
                            <p>المبلغ الواصل: <strong>${parseFloat(receivedAmountInput.value || 0).toLocaleString()} د.ع</strong></p>
                            <p>المبلغ المتبقي: <strong>${remainingAmount.toLocaleString()} د.ع</strong></p>
                        </div>

                        <div class="preview-actions">
                            <button id="print-from-preview" class="btn btn-warning">
                                <i class="fas fa-print"></i> طباعة الفاتورة
                            </button>
                            <button id="close-preview" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إغلاق المعاينة
                            </button>
                        </div>
                    </div>
                `;

                // Add event listeners for preview actions
                document.getElementById('print-from-preview').addEventListener('click', () => {
                    printProfessionalInvoice();
                });

                document.getElementById('close-preview').addEventListener('click', () => {
                    closePreviewModal();
                });

                openPreviewModal();
            }

            function createWorkStatusEditForm(data, index) {
                modalBody.innerHTML = `
                    <h3>تعديل بيانات موقف العمل</h3>
                    <div class="form-container">
                        <div class="form-group">
                            <label for="edit-date">التاريخ:</label>
                            <input type="date" id="edit-date" value="${data.date || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-quantity">الكمية:</label>
                            <input type="number" id="edit-quantity" value="${data.quantity || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-cement-type-text">السمنت:</label>
                            <input type="text" id="edit-cement-type-text" value="${data.cementTypeText || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-price-per-meter">سعر المتر:</label>
                            <input type="number" id="edit-price-per-meter" value="${data.pricePerMeter || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-pump-wage">أجرة البم:</label>
                            <input type="number" id="edit-pump-wage" value="${data.pumpWage || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-total-amount">المبلغ:</label>
                            <input type="text" id="edit-total-amount" value="${data.totalAmount || ''}" readonly>
                        </div>
                        <div class="form-group">
                            <label for="edit-received-amount">المبلغ الواصل:</label>
                            <input type="number" id="edit-received-amount" value="${data.receivedAmount || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-cement-type-dropdown">نوع السمنت:</label>
                            <select id="edit-cement-type-dropdown">
                                <option value="">اختر نوع السمنت</option>
                                <option value="عادي" ${data.cementTypeDropdown === 'عادي' ? 'selected' : ''}>عادي</option>
                                <option value="مقاوم" ${data.cementTypeDropdown === 'مقاوم' ? 'selected' : ''}>مقاوم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-resistance">المقاومة:</label>
                            <select id="edit-resistance">
                                <option value="">اختر المقاومة</option>
                                <option value="250" ${data.resistance === '250' ? 'selected' : ''}>250</option>
                                <option value="300" ${data.resistance === '300' ? 'selected' : ''}>300</option>
                                <option value="350" ${data.resistance === '350' ? 'selected' : ''}>350</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-beneficiary-name">اسم المستفيد:</label>
                            <input type="text" id="edit-beneficiary-name" value="${data.beneficiaryName || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-details">التفاصيل:</label>
                            <textarea id="edit-details">${data.details || ''}</textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-added-by">المضاف:</label>
                            <input type="text" id="edit-added-by" value="${data.addedBy || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-payment-date">تاريخ التسديد:</label>
                            <input type="date" id="edit-payment-date" value="${data.paymentDate || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-phone-number">رقم الهاتف:</label>
                            <input type="text" id="edit-phone-number" value="${data.phoneNumber || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-notes">الملاحظات:</label>
                            <textarea id="edit-notes">${data.notes || ''}</textarea>
                        </div>
                        <div class="buttons-container">
                            <button id="save-work-status-edit" class="btn btn-success"><i class="fas fa-save"></i> حفظ التعديلات</button>
                            <button id="cancel-edit" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
                        </div>
                    </div>
                `;

                // Add calculation functionality
                const editQuantity = document.getElementById('edit-quantity');
                const editPricePerMeter = document.getElementById('edit-price-per-meter');
                const editPumpWage = document.getElementById('edit-pump-wage');
                const editTotalAmount = document.getElementById('edit-total-amount');

                function calculateEditTotal() {
                    const quantity = parseFloat(editQuantity.value) || 0;
                    const pricePerMeter = parseFloat(editPricePerMeter.value) || 0;
                    const pumpWage = parseFloat(editPumpWage.value) || 0;
                    const total = (quantity * pricePerMeter) + pumpWage;
                    editTotalAmount.value = total.toFixed(2);
                }

                editQuantity.addEventListener('input', calculateEditTotal);
                editPricePerMeter.addEventListener('input', calculateEditTotal);
                editPumpWage.addEventListener('input', calculateEditTotal);

                document.getElementById('save-work-status-edit').addEventListener('click', () => {
                    const updatedData = {
                        date: document.getElementById('edit-date').value,
                        quantity: parseFloat(document.getElementById('edit-quantity').value) || 0,
                        cementTypeText: document.getElementById('edit-cement-type-text').value,
                        pricePerMeter: parseFloat(document.getElementById('edit-price-per-meter').value) || 0,
                        pumpWage: parseFloat(document.getElementById('edit-pump-wage').value) || 0,
                        totalAmount: parseFloat(document.getElementById('edit-total-amount').value) || 0,
                        receivedAmount: parseFloat(document.getElementById('edit-received-amount').value) || 0,
                        cementTypeDropdown: document.getElementById('edit-cement-type-dropdown').value,
                        resistance: document.getElementById('edit-resistance').value,
                        beneficiaryName: document.getElementById('edit-beneficiary-name').value,
                        details: document.getElementById('edit-details').value,
                        addedBy: document.getElementById('edit-added-by').value,
                        paymentDate: document.getElementById('edit-payment-date').value,
                        phoneNumber: document.getElementById('edit-phone-number').value,
                        notes: document.getElementById('edit-notes').value
                    };

                    workStatusData[index] = updatedData;
                    localStorage.setItem('workStatusData', JSON.stringify(workStatusData));
                    applyWorkStatusFilter();
                    closeEditModal();
                    alert('تم تحديث البيانات بنجاح!');
                });

                document.getElementById('cancel-edit').addEventListener('click', closeEditModal);
            }



            function createExpenseEditForm(data, index) {
                modalBody.innerHTML = `
                    <h3>تعديل بيانات المصروف</h3>
                    <div class="form-container">
                        <div class="form-group">
                            <label for="edit-expense-date">التاريخ:</label>
                            <input type="date" id="edit-expense-date" value="${data.date || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-expense-amount">المبلغ (بالدينار العراقي):</label>
                            <input type="number" id="edit-expense-amount" value="${data.amount || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-expense-details">التفاصيل:</label>
                            <textarea id="edit-expense-details">${data.details || ''}</textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-expense-source">جهة الصرف:</label>
                            <input type="text" id="edit-expense-source" value="${data.source || ''}">
                        </div>
                        <div class="form-group">
                            <label for="edit-expense-status">الحالة:</label>
                            <select id="edit-expense-status">
                                <option value="غير مسدد" ${data.status === 'غير مسدد' ? 'selected' : ''}>غير مسدد</option>
                                <option value="مسدد" ${data.status === 'مسدد' ? 'selected' : ''}>مسدد</option>
                            </select>
                        </div>
                        <div class="buttons-container">
                            <button id="save-expense-edit" class="btn btn-success"><i class="fas fa-save"></i> حفظ التعديلات</button>
                            <button id="cancel-expense-edit" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
                        </div>
                    </div>
                `;

                document.getElementById('save-expense-edit').addEventListener('click', () => {
                    const updatedData = {
                        date: document.getElementById('edit-expense-date').value,
                        amount: parseFloat(document.getElementById('edit-expense-amount').value) || 0,
                        details: document.getElementById('edit-expense-details').value,
                        source: document.getElementById('edit-expense-source').value,
                        status: document.getElementById('edit-expense-status').value
                    };

                    expensesData[index] = updatedData;
                    localStorage.setItem('expensesData', JSON.stringify(expensesData));
                    displayDashboardExpensesData(expensesData);
                    closeEditModal();
                    alert('تم تحديث المصروف بنجاح!');
                });

                document.getElementById('cancel-expense-edit').addEventListener('click', closeEditModal);
            }

            // Employee Management Functions
            function displayEmployeesData() {
                const employeesTableBody = document.getElementById('employees-table-body');
                employeesTableBody.innerHTML = '';

                savedEmployees.forEach((employee, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${employee.name}</td>
                        <td>${employee.phone}</td>
                        <td>${employee.position}</td>
                        <td>
                            <button class="edit-button" data-index="${index}">تعديل</button>
                            <button class="delete-button" data-index="${index}">حذف</button>
                        </td>
                    `;
                    employeesTableBody.appendChild(row);
                });
            }

            const addEmployeeButton = document.getElementById('add-employee-button');
            const clearEmployeeButton = document.getElementById('clear-employee-button');

            if (addEmployeeButton) {
                addEmployeeButton.addEventListener('click', () => {
                    const name = document.getElementById('employee-name-input').value.trim();
                    const phone = document.getElementById('employee-phone-input').value.trim();
                    const position = document.getElementById('employee-position').value.trim();

                    if (!name) {
                        alert('الرجاء إدخال اسم الموظف');
                        return;
                    }

                    const newEmployee = { name, phone, position };
                    savedEmployees.push(newEmployee);
                    localStorage.setItem('savedEmployees', JSON.stringify(savedEmployees));

                    displayEmployeesData();
                    populateEmployeeNames();

                    // Clear form
                    document.getElementById('employee-name-input').value = '';
                    document.getElementById('employee-phone-input').value = '';
                    document.getElementById('employee-position').value = '';

                    alert('تم إضافة الموظف بنجاح!');
                });
            }

            if (clearEmployeeButton) {
                clearEmployeeButton.addEventListener('click', () => {
                    document.getElementById('employee-name-input').value = '';
                    document.getElementById('employee-phone-input').value = '';
                    document.getElementById('employee-position').value = '';
                });
            }

            // Handle employee deletion and editing
            document.body.addEventListener('click', (e) => {
                const target = e.target;
                const index = target.dataset.index;

                if (target.closest('#employees-table') && index !== undefined) {
                    if (target.classList.contains('delete-button')) {
                        if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                            savedEmployees.splice(index, 1);
                            localStorage.setItem('savedEmployees', JSON.stringify(savedEmployees));
                            displayEmployeesData();
                            populateEmployeeNames();
                        }
                    } else if (target.classList.contains('edit-button')) {
                        const employee = savedEmployees[index];
                        document.getElementById('employee-name-input').value = employee.name;
                        document.getElementById('employee-phone-input').value = employee.phone;
                        document.getElementById('employee-position').value = employee.position;

                        // Remove the old employee and let user add the updated one
                        savedEmployees.splice(index, 1);
                        localStorage.setItem('savedEmployees', JSON.stringify(savedEmployees));
                        displayEmployeesData();
                        populateEmployeeNames();
                    }
                }
            });

            // Event listeners لأزرار الاستيراد والتصدير
            const downloadWorkStatusExcelButton = document.getElementById('download-work-status-excel');
            const printStatementButton = document.getElementById('print-statement-button');
            const importWorkStatusButton = document.getElementById('import-work-status-button');
            const importWorkStatusExcel = document.getElementById('import-work-status-excel');

            if (downloadWorkStatusExcelButton) {
                downloadWorkStatusExcelButton.addEventListener('click', downloadFilteredWorkStatusAsExcel);
            }

            if (printStatementButton) {
                printStatementButton.addEventListener('click', printStatement);
            }

            if (importWorkStatusButton) {
                importWorkStatusButton.addEventListener('click', () => {
                    importWorkStatusExcel.click();
                });
            }

            if (importWorkStatusExcel) {
                importWorkStatusExcel.addEventListener('change', handleExcelImport);
            }

            // دالة استيراد ملف Excel
            function handleExcelImport(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet);

                        if (jsonData.length === 0) {
                            alert('الملف فارغ أو لا يحتوي على بيانات صالحة.');
                            return;
                        }

                        // تحويل البيانات إلى التنسيق المطلوب
                        const importedData = jsonData.map(row => ({
                            date: formatExcelDate(row['التاريخ'] || row['Date'] || ''),
                            quantity: parseFloat(row['الكمية'] || row['Quantity'] || 0),
                            cementTypeText: row['السمنت'] || row['Cement'] || '',
                            pricePerMeter: parseFloat(row['سعر المتر'] || row['Price per Meter'] || 0),
                            pumpWage: parseFloat(row['أجرة البم'] || row['Pump Wage'] || 0),
                            totalAmount: parseFloat(row['المبلغ'] || row['Total Amount'] || 0),
                            receivedAmount: parseFloat(row['الواصل'] || row['Received Amount'] || 0),
                            cementTypeDropdown: row['نوع السمنت'] || row['Cement Type'] || '',
                            resistance: row['المقاومة'] || row['Resistance'] || '',
                            beneficiaryName: row['اسم المستفيد'] || row['Beneficiary Name'] || '',
                            details: row['التفاصيل'] || row['Details'] || '',
                            addedBy: row['المضاف'] || row['Added By'] || '',
                            employeeName: row['اسم الموظف'] || row['Employee Name'] || '',
                            paymentDate: formatExcelDate(row['تاريخ التسديد'] || row['Payment Date'] || ''),
                            phoneNumber: row['رقم الهاتف'] || row['Phone Number'] || '',
                            notes: row['الملاحظات'] || row['Notes'] || ''
                        }));

                        // إضافة البيانات المستوردة إلى البيانات الموجودة
                        const confirmImport = confirm(`تم العثور على ${importedData.length} سجل في الملف. هل تريد إضافتها إلى البيانات الموجودة؟`);

                        if (confirmImport) {
                            workStatusData.push(...importedData);
                            localStorage.setItem('workStatusData', JSON.stringify(workStatusData));
                            displayDashboardWorkStatusData(workStatusData);
                            alert(`تم استيراد ${importedData.length} سجل بنجاح!`);
                        }

                    } catch (error) {
                        console.error('خطأ في قراءة الملف:', error);
                        alert('حدث خطأ في قراءة الملف. تأكد من أن الملف بتنسيق Excel صحيح.');
                    }
                };
                reader.readAsArrayBuffer(file);

                // إعادة تعيين قيمة input لتمكين اختيار نفس الملف مرة أخرى
                event.target.value = '';
            }

            // دالة تحويل تاريخ Excel إلى تنسيق صحيح
            function formatExcelDate(excelDate) {
                if (!excelDate) return '';

                // إذا كان التاريخ رقم (Excel date serial)
                if (typeof excelDate === 'number') {
                    const date = new Date((excelDate - 25569) * 86400 * 1000);
                    return date.toISOString().split('T')[0];
                }

                // إذا كان التاريخ نص
                if (typeof excelDate === 'string') {
                    const date = new Date(excelDate);
                    if (!isNaN(date.getTime())) {
                        return date.toISOString().split('T')[0];
                    }
                }

                return excelDate.toString();
            }

            // Initial data display on load
            populatePhoneNumbers();
            populateEmployeeNames();
            displayContactsData();
            displayCredentials();
            displayDashboardExpensesData(expensesData);
            displayDashboardWorkStatusData(workStatusData);
        });
    </script>
</body>
</html>